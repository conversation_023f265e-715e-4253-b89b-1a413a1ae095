--查询日维度三区实付、PHF数据
USE CAMDB
GO
DECLARE @DataDate bigint = 20250316
select c.*,ISNULL(p.PHFOrderCnt,0) 拼好饭订单量 from (
select 
DataDate 日期,
CityName 城市名称,
餐饮实付=SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END),
非餐实付=SUM(CASE WHEN Category<>'餐饮'  THEN RealCollect ELSE 0 END)
from tbCateringRealCollect_New
where DataDate>=@DataDate
AND CityName NOT IN('雄县','阳江市','廉江市','阳西','阳山县')
GROUP BY DataDate,
CityName
)c
left join (
select CityName,DataDate,PHFOrderCnt from tbPHFOrderCnt
where DataDate>=@DataDate
)p on c.城市名称=p.CityName and c.日期=p.DataDate
ORDER BY 日期
