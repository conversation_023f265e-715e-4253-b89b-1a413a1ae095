# SQL Server 连接异常排查指南

## 🚨 错误信息分析
```
Microsoft.Data.SqlClient.SqlException (0x80131904):
A connection was successfully established with the server, but then an error occurred during the pre-login handshake.
(provider: TCP Provider, error: 0 - Undefined error: 0)
```

## 🔍 问题排查步骤

### 1. 检查连接字符串配置

#### ✅ 推荐的连接字符串格式：
```csharp
// 基础连接字符串
"Server=服务器地址;Database=CAMDB;Trusted_Connection=true;"

// 带用户名******
"Server=服务器地址;Database=CAMDB;User Id=用户名;Password=******;"

// 添加加密和超时设置
"Server=服务器地址;Database=CAMDB;User Id=用户名;Password=******;Encrypt=false;TrustServerCertificate=true;Connection Timeout=30;"
```

#### 🔧 关键参数说明：
- **Encrypt=false**: 禁用 SSL 加密（如果服务器不支持）
- **TrustServerCertificate=true**: 信任服务器证书
- **Connection Timeout=30**: 设置连接超时时间
- **MultipleActiveResultSets=true**: 允许多个活动结果集

### 2. 网络连接检查

#### 检查服务器可达性：
```bash
# 检查服务器是否可达
ping 服务器IP地址

# 检查 SQL Server 端口（默认1433）
telnet 服务器IP地址 1433

# 或使用 nc 命令
nc -zv 服务器IP地址 1433
```

#### 检查防火墙设置：
- Windows 防火墙是否允许 SQL Server 端口
- 网络防火墙是否阻止连接
- SQL Server 是否启用了 TCP/IP 协议

### 3. SQL Server 服务器端检查

#### 检查 SQL Server 配置：
1. **SQL Server Configuration Manager**
   - 确认 TCP/IP 协议已启用
   - 检查端口配置（默认1433）
   - 确认 SQL Server 服务正在运行

2. **SQL Server 实例状态**
   ```sql
   -- 检查服务器信息
   SELECT @@VERSION
   SELECT @@SERVERNAME
   SELECT SERVERPROPERTY('InstanceName')
   ```

3. **网络协议设置**
   - 启用 TCP/IP 协议
   - 设置正确的端口号
   - 重启 SQL Server 服务

### 4. 客户端配置检查

#### .NET 应用程序配置：
```xml
<!-- app.config 或 web.config -->
<configuration>
  <connectionStrings>
    <add name="CAMDB" 
         connectionString="Server=服务器地址;Database=CAMDB;User Id=用户名;Password=******;Encrypt=false;TrustServerCertificate=true;Connection Timeout=30;" 
         providerName="Microsoft.Data.SqlClient" />
  </connectionStrings>
</configuration>
```

#### 代码中的连接处理：
```csharp
using Microsoft.Data.SqlClient;

try
{
    using (var connection = new SqlConnection(connectionString))
    {
        connection.Open();
        // 执行 SQL 操作
    }
}
catch (SqlException ex)
{
    Console.WriteLine($"SQL Error: {ex.Message}");
    Console.WriteLine($"Error Number: {ex.Number}");
    Console.WriteLine($"Severity: {ex.Class}");
    Console.WriteLine($"State: {ex.State}");
}
```

### 5. 常见解决方案

#### 方案 1: 修改连接字符串
```csharp
// 添加加密相关参数
var connectionString = "Server=服务器地址;Database=CAMDB;User Id=用户名;Password=******;Encrypt=false;TrustServerCertificate=true;";
```

#### 方案 2: 检查 TLS/SSL 版本
```csharp
// 在连接前设置 TLS 版本
System.Net.ServicePointManager.SecurityProtocol = 
    System.Net.SecurityProtocolType.Tls12 | 
    System.Net.SecurityProtocolType.Tls11 | 
    System.Net.SecurityProtocolType.Tls;
```

#### 方案 3: 使用命名实例
```csharp
// 如果使用命名实例
var connectionString = "Server=服务器地址\\实例名;Database=CAMDB;...";
```

#### 方案 4: 启用详细日志
```csharp
// 启用 SqlClient 日志记录
using Microsoft.Data.SqlClient;

SqlConnection.ClearAllPools();
// 添加连接重试逻辑
```

### 6. 针对您的 SQL 文件的建议

基于您的 `补贴分析交易底数.sql` 文件：

#### 优化建议：
1. **添加错误处理**：
```sql
BEGIN TRY
    USE [CAMDB]
    DECLARE @DataDate bigint=20250727
    
    -- 您的查询逻辑
    select t.*,
    ISNULL(p.拼好饭订单量,0) 拼好饭订单量,
    剔除PHF后餐饮订单量=t.餐饮订单量-ISNULL(p.拼好饭订单量,0) 
    from(
        -- 原有查询
    )t
    left join (
        -- 原有 join 逻辑
    )p on t.城市名称=p.城市名称 and t.日期=p.日期
    
END TRY
BEGIN CATCH
    SELECT 
        ERROR_NUMBER() AS ErrorNumber,
        ERROR_MESSAGE() AS ErrorMessage,
        ERROR_SEVERITY() AS ErrorSeverity,
        ERROR_STATE() AS ErrorState
END CATCH
```

2. **添加连接测试**：
```sql
-- 测试连接和权限
SELECT 
    DB_NAME() as CurrentDatabase,
    USER_NAME() as CurrentUser,
    GETDATE() as ServerTime
```

### 7. 快速诊断命令

#### 在应用程序中添加诊断代码：
```csharp
public static void DiagnoseConnection(string connectionString)
{
    try
    {
        using (var connection = new SqlConnection(connectionString))
        {
            Console.WriteLine("尝试连接到数据库...");
            connection.Open();
            Console.WriteLine("连接成功！");
            
            using (var command = new SqlCommand("SELECT @@VERSION", connection))
            {
                var version = command.ExecuteScalar();
                Console.WriteLine($"SQL Server 版本: {version}");
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"连接失败: {ex.Message}");
        Console.WriteLine($"异常类型: {ex.GetType().Name}");
        if (ex.InnerException != null)
        {
            Console.WriteLine($"内部异常: {ex.InnerException.Message}");
        }
    }
}
```

## 🎯 立即行动建议

1. **首先尝试**: 在连接字符串中添加 `Encrypt=false;TrustServerCertificate=true;`
2. **检查网络**: 确认能够 ping 通服务器并且端口开放
3. **验证权限**: 确认数据库用户有访问 CAMDB 数据库的权限
4. **测试简单查询**: 先用简单的 `SELECT 1` 测试连接
5. **查看服务器日志**: 检查 SQL Server 错误日志获取更多信息

## 📞 如需进一步帮助

如果问题仍然存在，请提供：
- 完整的连接字符串（隐藏敏感信息）
- SQL Server 版本和配置
- 网络环境信息
- 应用程序运行环境
