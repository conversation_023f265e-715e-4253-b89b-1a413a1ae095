{"metadata": {"kernelspec": {"name": "SQL", "display_name": "SQL", "language": "sql"}, "language_info": {"name": "sql", "version": ""}}, "nbformat_minor": 2, "nbformat": 4, "cells": [{"cell_type": "markdown", "source": ["查询指定日期商家列表"], "metadata": {"azdata_cell_guid": "dd86ff7d-0b89-4a3a-ae49-d05cd59524cd"}}, {"cell_type": "code", "source": ["--查询日维度三区实付、PHF数据\n", "USE CAMDB\n", "GO\n", "DECLARE @DataDate bigint = 20250316\n", "select c.*,ISNULL(p.PHFOrderCnt,0) 拼好饭订单量 from (\n", "select \n", "DataDate 日期,\n", "CityName 城市名称,\n", "餐饮实付=SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END),\n", "非餐实付=SUM(CASE WHEN Category<>'餐饮'  THEN RealCollect ELSE 0 END)\n", "from tbCateringRealCollect_New\n", "where DataDate>=@DataDate\n", "AND CityName NOT IN('雄县','阳江市','廉江市','阳西','阳山县')\n", "GROUP BY DataDate,\n", "CityName\n", ")c\n", "left join (\n", "select CityName,DataDate,PHFOrderCnt from tbPHFOrderCnt\n", "where DataDate>=@DataDate\n", ")p on c.城市名称=p.<PERSON> and c.日期=p.DataDate\n", "ORDER BY 日期\n", ""], "metadata": {"azdata_cell_guid": "e99037a7-a02f-401a-aa52-c3fd170ead03", "language": "sql", "tags": []}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--月维度实付 GMV\n", "USE [CAMDB]\n", "GO\n", "select CityName 城市名称,\n", "\n", "餐饮实付交易额 =SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END)  \n", "from tbCateringRealCollect_New\n", "where DataYear=2025\n", "and DataMonth=2\n", "AND CityName not in ('德庆','阳江市','廉江市','阳西','四会市','封开县','台山市','双水镇')\n", "group by CityName"], "metadata": {"azdata_cell_guid": "2b7684bc-0b35-4f4f-9fcb-1addd80a1c49", "language": "sql", "tags": ["hide_input"]}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--KA周期内减配&满减补贴数据\n", "SELECT \n", "    活动业务端类型,\n", "    活动大类,\n", "    商家类型,\n", "    商家ID,\n", "    SUM(代理商补贴金额) AS 代理商补贴金额,\n", "    SUM(商家补贴金额) AS 商家补贴金额\n", "FROM (\n", "    SELECT \n", "        日,\n", "        外卖组织结构,\n", "        是否三区城市 = (CASE WHEN 外卖组织结构 IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') THEN 1 ELSE 0 END),\n", "        商家ID,\n", "        商家类型,\n", "        活动业务端类型,\n", "        活动大类,\n", "        CONVERT(decimal(18,4), 代理商补贴金额) AS 代理商补贴金额,\n", "        CONVERT(decimal(18,4), 商家补贴金额) AS 商家补贴金额\n", "    FROM Temp_DB.dbo.daibu202504\n", ") t\n", "WHERE 日 >= '20250414' AND 日 < '20250422'\n", "AND 活动大类 IN ('满减活动', '减配送费')  \n", "AND 商家类型 LIKE '%KA'\n", "GROUP BY \n", "    活动业务端类型,\n", "    活动大类,\n", "    商家类型,\n", "    商家ID"], "metadata": {"azdata_cell_guid": "de7824db-4edb-4935-bb29-572bd6cc5c2f", "language": "sql", "tags": ["hide_input"]}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--#BC补 查询日维度交易数据\n", "DECLARE @DataDate bigint=20250428\n", "select t.*,\n", "ISNULL(p.拼好饭订单量,0) 拼好饭订单量,\n", "剔除PHF后餐饮订单量=t.餐饮订单量-ISNULL(p.拼好饭订单量,0) \n", "from(\n", "select CityName 城市名称,\n", "是否三区城市=(CASE WHEN CityName in ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') then 1 else 0 END),\n", "DataDate 日期,\n", "餐饮订单量=SUM(CASE WHEN Category='餐饮' THEN OrderCnt ELSE 0 END),\n", "餐饮实付交易额 =SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) ,\n", "非餐实付交易额 =SUM(CASE WHEN Category<>'餐饮' AND One_Category<>'数码家电' THEN RealCollect ELSE 0 END) ,\n", "数码家电实付交易额 =SUM(CASE WHEN Category<>'餐饮' AND One_Category='数码家电' THEN RealCollect ELSE 0 END),\n", "餐饮原价交易额=SUM(CASE WHEN Category='餐饮' THEN OrginalCost ELSE 0 END) , \n", "非餐原价交易额=SUM(CASE WHEN Category<>'餐饮' THEN OrginalCost ELSE 0 END) \n", "from tbCateringRealCollect_New\n", "where DataYear=2025 and DataMonth=4\n", "AND DataDate>=@DataDate\n", "AND CityName NOT in('廉江市','阳江市','阳西')\n", "group by CityName,\n", "DataDate\n", ")t\n", "left join (\n", "select CityName 城市名称,DataDate 日期,SUM(PHFOrderCnt) 拼好饭订单量 from tbPHFOrderCnt\n", "where DataYear=2025 and DataMonth=4\n", "AND CityName in('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县','雄县')\n", "group by CityName,DataDate\n", ")p on t.城市名称=p.城市名称 and t.日期=p.日期"], "metadata": {"azdata_cell_guid": "f1e98313-c4b8-45c5-8a76-0ebe642829e7", "language": "sql", "tags": []}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--#BC补 查询日维度补贴数据 \n", "select\n", "t.日,\n", "外卖组织结构,\n", "是否三区城市,\n", "新商家类型,\n", "活动业务端类型,\n", "活动大类,\n", "品类,\n", "sum(代理商补贴金额) 代理商补贴金额,\n", "SUM(商家补贴金额) 商家补贴金额\n", "from (\n", "select\n", "日,\n", "外卖组织结构,\n", "是否三区城市=(CASE WHEN 外卖组织结构 in ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') then 1 else 0 END),\n", "商家ID,\n", "商家类型,\n", "活动业务端类型,\n", "活动大类,\n", "新商家类型=(CASE WHEN 商家类型='城市商家' AND 一级品类 IN ('美食','甜点','饮品') THEN '餐饮城商'\n", "WHEN 商家类型<>'城市商家' AND 一级品类 IN ('美食','甜点','饮品') THEN '餐饮KA'\n", "WHEN 商家类型='闪购城市商家' AND 一级品类 not IN ('美食','甜点','饮品') THEN '闪购城商'\n", "ELSE '闪购KA' END\n", "),\n", "品类 = (CASE WHEN 一级品类 IN ('美食','甜点','饮品') THEN '餐饮' ELSE '非餐' END),\n", "CONVERT(decimal(18,4),代理商补贴金额) 代理商补贴金额,\n", "CONVERT(decimal(18,4),商家补贴金额) 商家补贴金额\n", "from Temp_DB.dbo.daibu202504\n", ")t\n", "where 日>='20250428'\n", "group by t.日,\n", "外卖组织结构,\n", "是否三区城市,\n", "新商家类型,\n", "活动业务端类型,\n", "活动大类,\n", "品类\n", "order by 日"], "metadata": {"language": "sql", "azdata_cell_guid": "4afbee7b-41c5-4d24-902f-9b6a65b48234", "tags": []}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--商家绩效减配达标天数\n", "select 商家ID,当月营业天数,[重点活动（减配）达标天数-月],统计日期 from BussinessInfo.dbo.tbBussiness_XQ\n", "where 统计日期=20250424"], "metadata": {"azdata_cell_guid": "7a866855-1eb7-439f-bb6e-b66a7900d168", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--商家日维度交易额\n", "SELECT \n", "日,\n", "外卖组织结构,\n", "一级商家配送类型,\n", "一级品类,\n", "二级品类,\n", "三级品类,\n", "商家类型,\n", "代理商家分级标签,\n", "商家营业状态,\n", "商家ID,\n", "商家名称,\n", "合作BD,\n", "[实付交易额（GMV）],\n", "[原价交易额（GMV）],\n", "[订单量（GMV）],\n", "[合作商补贴金额（除满赠和买赠）],\n", "[商家补贴金额（除满赠和买赠）]\n", "FROM BussinessInfo.dbo.tbBS202504\n", "where 商家类型 LIKE '%KA'\n", "AND 外卖组织结构 NOT IN('雄县','阳江市','廉江市','阳西','阳山县')\n", "AND 日>20250413"], "metadata": {"azdata_cell_guid": "ac1be7a4-991f-4677-b88e-ffd605d4a780", "language": "sql", "tags": []}, "outputs": [], "execution_count": 1}, {"cell_type": "code", "source": ["--#UE 日维度phf补贴金额\n", "select 日,外卖组织结构,商家ID,商家名称,活动业务端类型,活动大类,活动订单原价交易额,活动订单量,代理商补贴金额,商家补贴金额\n", "from Temp_DB.dbo.daibu202504\n", "where  活动大类 LIKE '%拼%'\n", "and 活动大类<>'拼单满减'"], "metadata": {"azdata_cell_guid": "7de79410-9a85-4deb-81f0-b91c6689d82c", "language": "sql", "tags": []}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--#P<PERSON> 菜品明细\n", "select * from tbPHFGoodsDsc_D\n", "where 日=20250610\n", "\n", "--#P<PERSON> 商家明细\n", "select * from tbPHFBusiness_202506\n", "where 日=20250610"], "metadata": {"azdata_cell_guid": "b82865b0-65ee-41fd-bf10-d0c4c3a6970d", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--查询原价数据\n", "USE CAMDB\n", "GO\n", "DECLARE @DataDate bigint = 20250301\n", "\n", "select \n", "DataDate 日期,\n", "CityName 城市名称,\n", "餐饮原价=SUM(CASE WHEN Category='餐饮' THEN OrginalCost ELSE 0 END),\n", "非餐原价=SUM(CASE WHEN Category<>'餐饮'  THEN OrginalCost ELSE 0 END)\n", "from tbCateringRealCollect_New\n", "where DataDate>=@DataDate\n", "AND CityName  IN('阳山县')\n", "group by DataDate,CityName\n", "order by DataDate"], "metadata": {"azdata_cell_guid": "3091615a-d56e-4bb6-9e9d-062c869b9210", "language": "sql", "tags": []}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["--商家补贴明细\n", "select * from Temp_DB.dbo.daibu202506\n", "where 外卖组织结构='阳江市'\n", "AND 日 =20250610"], "metadata": {"azdata_cell_guid": "5a244f86-4924-4997-8d18-550ed474a5dd", "language": "sql"}, "outputs": [], "execution_count": null}]}