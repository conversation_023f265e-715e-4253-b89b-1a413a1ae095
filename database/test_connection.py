#!/usr/bin/env python3
"""
SQL Server 连接测试脚本
用于诊断 SQL Server 连接问题
"""

import socket
import subprocess
import sys
from datetime import datetime

def test_ping(server_ip):
    """测试服务器是否可达"""
    print(f"🔍 测试服务器 {server_ip} 是否可达...")
    try:
        # 在 macOS/Linux 上使用 ping
        result = subprocess.run(['ping', '-c', '3', server_ip], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ 服务器 {server_ip} 可达")
            return True
        else:
            print(f"❌ 服务器 {server_ip} 不可达")
            return False
    except Exception as e:
        print(f"❌ Ping 测试失败: {e}")
        return False

def test_port(server_ip, port=1433):
    """测试 SQL Server 端口是否开放"""
    print(f"🔍 测试端口 {server_ip}:{port} 是否开放...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((server_ip, port))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口 {port} 开放")
            return True
        else:
            print(f"❌ 端口 {port} 关闭或被阻止")
            return False
    except Exception as e:
        print(f"❌ 端口测试失败: {e}")
        return False

def test_sql_connection():
    """测试 SQL Server 连接（需要安装 pyodbc）"""
    try:
        import pyodbc
        print("🔍 测试 SQL Server 连接...")
        
        # 请替换为您的实际连接信息
        server = 'YOUR_SERVER'  # 替换为您的服务器地址
        database = 'CAMDB'
        username = 'YOUR_USERNAME'  # 替换为您的用户名
        password = 'YOUR_PASSWORD'  # 替换为您的密码
        
        # 不同的连接字符串尝试
        connection_strings = [
            f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};Encrypt=no;TrustServerCertificate=yes;',
            f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};',
            f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes;Encrypt=no;'
        ]
        
        for i, conn_str in enumerate(connection_strings, 1):
            try:
                print(f"尝试连接方式 {i}...")
                conn = pyodbc.connect(conn_str, timeout=10)
                cursor = conn.cursor()
                cursor.execute("SELECT @@VERSION")
                version = cursor.fetchone()[0]
                print(f"✅ 连接成功！SQL Server 版本: {version[:50]}...")
                conn.close()
                return True
            except Exception as e:
                print(f"❌ 连接方式 {i} 失败: {e}")
                continue
        
        print("❌ 所有连接方式都失败了")
        return False
        
    except ImportError:
        print("⚠️  pyodbc 未安装，跳过 SQL 连接测试")
        print("   安装命令: pip install pyodbc")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 SQL Server 连接诊断工具")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 请替换为您的实际服务器 IP
    server_ip = input("请输入 SQL Server 服务器 IP 地址: ").strip()
    if not server_ip:
        print("❌ 请提供有效的服务器 IP 地址")
        return
    
    port = input("请输入端口号 (默认 1433): ").strip()
    if not port:
        port = 1433
    else:
        try:
            port = int(port)
        except ValueError:
            print("❌ 端口号必须是数字")
            return
    
    print("\n" + "=" * 60)
    print("🚀 开始诊断...")
    print("=" * 60)
    
    # 测试步骤
    ping_ok = test_ping(server_ip)
    port_ok = test_port(server_ip, port)
    sql_ok = test_sql_connection()
    
    print("\n" + "=" * 60)
    print("📊 诊断结果汇总")
    print("=" * 60)
    print(f"服务器可达性: {'✅ 通过' if ping_ok else '❌ 失败'}")
    print(f"端口连通性: {'✅ 通过' if port_ok else '❌ 失败'}")
    if sql_ok is not None:
        print(f"SQL 连接: {'✅ 通过' if sql_ok else '❌ 失败'}")
    else:
        print("SQL 连接: ⚠️  未测试 (缺少 pyodbc)")
    
    print("\n" + "=" * 60)
    print("💡 建议解决方案")
    print("=" * 60)
    
    if not ping_ok:
        print("1. 检查服务器 IP 地址是否正确")
        print("2. 检查网络连接")
        print("3. 检查防火墙设置")
    
    if ping_ok and not port_ok:
        print("1. 检查 SQL Server 是否启动")
        print("2. 检查 TCP/IP 协议是否启用")
        print("3. 检查端口配置")
        print("4. 检查防火墙是否阻止端口")
    
    if ping_ok and port_ok and not sql_ok:
        print("1. 检查用户名和密码")
        print("2. 检查数据库权限")
        print("3. 尝试添加连接参数: Encrypt=false;TrustServerCertificate=true;")
        print("4. 检查 SQL Server 认证模式")

if __name__ == "__main__":
    main()
