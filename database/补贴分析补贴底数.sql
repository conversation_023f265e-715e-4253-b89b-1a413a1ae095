--补贴分析补贴底数
USE [CAMDB]
GO
select
t.日,
外卖组织结构,
是否三区城市,
新商家类型,
活动业务端类型,
活动大类,
品类,
sum(代理商补贴金额) 代理商补贴金额,
SUM(商家补贴金额) 商家补贴金额,
SUM(美团补贴金额)美团补贴金额
from (
select
日,
外卖组织结构,
是否三区城市=(CASE WHEN 外卖组织结构 in ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') then 1 else 0 END),
商家ID,
商家类型,
活动业务端类型,
活动大类,
新商家类型=(CASE WHEN 商家类型='城市商家' AND 一级品类 IN ('美食','甜点','饮品') THEN '餐饮城商'
WHEN 商家类型<>'城市商家' AND 一级品类 IN ('美食','甜点','饮品') THEN '餐饮KA'
WHEN 商家类型='闪购城市商家' AND 一级品类 not IN ('美食','甜点','饮品') THEN '闪购城商'
ELSE '闪购KA' END
),
品类 = (CASE WHEN 一级品类 IN ('美食','甜点','饮品') THEN '餐饮' ELSE '非餐' END),
CONVERT(decimal(18,4),代理商补贴金额) 代理商补贴金额,
CONVERT(decimal(18,4),商家补贴金额) 商家补贴金额,
CONVERT(decimal(18,4),美团补贴金额) 美团补贴金额
from Temp_DB.dbo.daibu202507
)t
where 日>=20250726
group by t.日,
外卖组织结构,
是否三区城市,
新商家类型,
活动业务端类型,
活动大类,
品类
order by 日