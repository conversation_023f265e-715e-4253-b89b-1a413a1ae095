using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;

namespace SqlConnectionTest
{
    /// <summary>
    /// SQL Server 连接测试工具
    /// 用于诊断和解决连接问题
    /// </summary>
    public class SqlConnectionDiagnostic
    {
        /// <summary>
        /// 测试多种连接字符串配置
        /// </summary>
        /// <param name="server">服务器地址</param>
        /// <param name="database">数据库名</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        public static async Task TestMultipleConnectionStrings(string server, string database, string username, string password)
        {
            Console.WriteLine("🔧 SQL Server 连接诊断工具");
            Console.WriteLine("=" * 50);
            Console.WriteLine($"⏰ 测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"🎯 目标服务器: {server}");
            Console.WriteLine($"🗄️  目标数据库: {database}");
            Console.WriteLine();

            // 不同的连接字符串配置
            var connectionConfigs = new[]
            {
                new {
                    Name = "标准连接 + 禁用加密",
                    ConnectionString = $"Server={server};Database={database};User Id={username};Password={password};Encrypt=false;TrustServerCertificate=true;Connection Timeout=30;"
                },
                new {
                    Name = "标准连接 + 信任证书",
                    ConnectionString = $"Server={server};Database={database};User Id={username};Password={password};TrustServerCertificate=true;Connection Timeout=30;"
                },
                new {
                    Name = "Windows 身份验证",
                    ConnectionString = $"Server={server};Database={database};Trusted_Connection=true;Encrypt=false;Connection Timeout=30;"
                },
                new {
                    Name = "标准连接 + 多活动结果集",
                    ConnectionString = $"Server={server};Database={database};User Id={username};Password={password};Encrypt=false;TrustServerCertificate=true;MultipleActiveResultSets=true;Connection Timeout=30;"
                },
                new {
                    Name = "最小配置",
                    ConnectionString = $"Server={server};Database={database};User Id={username};Password={password};"
                }
            };

            bool anySuccess = false;

            for (int i = 0; i < connectionConfigs.Length; i++)
            {
                var config = connectionConfigs[i];
                Console.WriteLine($"🔍 测试配置 {i + 1}: {config.Name}");
                
                try
                {
                    using (var connection = new SqlConnection(config.ConnectionString))
                    {
                        Console.WriteLine("   ⏳ 尝试连接...");
                        await connection.OpenAsync();
                        
                        Console.WriteLine("   ✅ 连接成功！");
                        
                        // 执行测试查询
                        using (var command = new SqlCommand("SELECT @@VERSION, @@SERVERNAME, DB_NAME(), USER_NAME(), GETDATE()", connection))
                        {
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                if (await reader.ReadAsync())
                                {
                                    Console.WriteLine($"   📊 SQL Server 版本: {reader.GetString(0).Substring(0, Math.Min(50, reader.GetString(0).Length))}...");
                                    Console.WriteLine($"   🖥️  服务器名称: {reader.GetString(1)}");
                                    Console.WriteLine($"   🗄️  当前数据库: {reader.GetString(2)}");
                                    Console.WriteLine($"   👤 当前用户: {reader.GetString(3)}");
                                    Console.WriteLine($"   ⏰ 服务器时间: {reader.GetDateTime(4):yyyy-MM-dd HH:mm:ss}");
                                }
                            }
                        }
                        
                        anySuccess = true;
                        Console.WriteLine($"   🎉 推荐使用此配置！");
                        break;
                    }
                }
                catch (SqlException sqlEx)
                {
                    Console.WriteLine($"   ❌ SQL 异常: {sqlEx.Message}");
                    Console.WriteLine($"   📝 错误号: {sqlEx.Number}");
                    Console.WriteLine($"   🔢 严重级别: {sqlEx.Class}");
                    Console.WriteLine($"   📍 状态: {sqlEx.State}");
                    
                    if (sqlEx.InnerException != null)
                    {
                        Console.WriteLine($"   🔗 内部异常: {sqlEx.InnerException.Message}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ 一般异常: {ex.Message}");
                    Console.WriteLine($"   📝 异常类型: {ex.GetType().Name}");
                }
                
                Console.WriteLine();
            }

            if (!anySuccess)
            {
                Console.WriteLine("❌ 所有连接配置都失败了！");
                Console.WriteLine();
                PrintTroubleshootingTips();
            }
        }

        /// <summary>
        /// 测试您的具体 SQL 查询
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        public static async Task TestYourQuery(string connectionString)
        {
            Console.WriteLine("🔍 测试您的补贴分析查询...");
            
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // 首先测试简单查询
                    using (var command = new SqlCommand("SELECT COUNT(*) FROM tbCateringRealCollect_New WHERE DataYear=2025", connection))
                    {
                        var count = await command.ExecuteScalarAsync();
                        Console.WriteLine($"✅ tbCateringRealCollect_New 表中 2025 年数据条数: {count}");
                    }
                    
                    // 测试 PHF 表
                    using (var command = new SqlCommand("SELECT COUNT(*) FROM tbPHFOrderCnt WHERE DataYear=2025", connection))
                    {
                        var count = await command.ExecuteScalarAsync();
                        Console.WriteLine($"✅ tbPHFOrderCnt 表中 2025 年数据条数: {count}");
                    }
                    
                    // 测试您的完整查询（限制结果数量）
                    string yourQuery = @"
                        DECLARE @DataDate bigint=20250727
                        select TOP 5 t.*,
                        ISNULL(p.拼好饭订单量,0) 拼好饭订单量,
                        剔除PHF后餐饮订单量=t.餐饮订单量-ISNULL(p.拼好饭订单量,0) 
                        from(
                        select CityName 城市名称,
                        是否三区城市=(CASE WHEN CityName in ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') then 1 else 0 END),
                        DataDate 日期,
                        餐饮订单量=SUM(CASE WHEN Category='餐饮' THEN OrderCnt ELSE 0 END),
                        餐饮实付交易额 =SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) ,
                        非餐实付交易额 =SUM(CASE WHEN Category<>'餐饮' AND One_Category<>'数码家电' THEN RealCollect ELSE 0 END) ,
                        数码家电实付交易额 =SUM(CASE WHEN Category<>'餐饮' AND One_Category='数码家电' THEN RealCollect ELSE 0 END),
                        餐饮原价交易额=SUM(CASE WHEN Category='餐饮' THEN OrginalCost ELSE 0 END) , 
                        非餐原价交易额=SUM(CASE WHEN Category<>'餐饮' THEN OrginalCost ELSE 0 END) 
                        from tbCateringRealCollect_New
                        where DataYear=2025 
                        AND DataDate>=@DataDate
                        AND CityName NOT in('廉江市','阳西')
                        group by CityName,
                        DataDate
                        )t
                        left join (
                        select CityName 城市名称,DataDate 日期,SUM(PHFOrderCnt) 拼好饭订单量 from tbPHFOrderCnt
                        where DataYear=2025 
                        AND CityName in('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县','雄县','阳江市')
                        group by CityName,DataDate
                        )p on t.城市名称=p.城市名称 and t.日期=p.日期";
                    
                    using (var command = new SqlCommand(yourQuery, connection))
                    {
                        command.CommandTimeout = 60; // 设置查询超时
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            int rowCount = 0;
                            while (await reader.ReadAsync() && rowCount < 5)
                            {
                                Console.WriteLine($"✅ 查询结果 {++rowCount}: 城市={reader["城市名称"]}, 日期={reader["日期"]}, 餐饮订单量={reader["餐饮订单量"]}");
                            }
                        }
                    }
                    
                    Console.WriteLine("🎉 您的查询执行成功！");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 查询执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 打印故障排除提示
        /// </summary>
        private static void PrintTroubleshootingTips()
        {
            Console.WriteLine("💡 故障排除建议:");
            Console.WriteLine("=" * 50);
            Console.WriteLine("1. 🔧 连接字符串问题:");
            Console.WriteLine("   - 添加 Encrypt=false;TrustServerCertificate=true;");
            Console.WriteLine("   - 检查服务器地址、端口、数据库名");
            Console.WriteLine("   - 验证用户名和密码");
            Console.WriteLine();
            Console.WriteLine("2. 🌐 网络问题:");
            Console.WriteLine("   - 检查服务器是否可达 (ping 测试)");
            Console.WriteLine("   - 检查端口 1433 是否开放");
            Console.WriteLine("   - 检查防火墙设置");
            Console.WriteLine();
            Console.WriteLine("3. 🛠️ SQL Server 配置:");
            Console.WriteLine("   - 确认 SQL Server 服务正在运行");
            Console.WriteLine("   - 启用 TCP/IP 协议");
            Console.WriteLine("   - 检查 SQL Server 认证模式");
            Console.WriteLine("   - 确认用户有访问数据库的权限");
            Console.WriteLine();
            Console.WriteLine("4. 🔐 安全设置:");
            Console.WriteLine("   - 检查 TLS/SSL 版本兼容性");
            Console.WriteLine("   - 验证证书设置");
            Console.WriteLine("   - 检查加密要求");
        }

        /// <summary>
        /// 主程序入口
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.WriteLine("请输入连接信息:");
            Console.Write("服务器地址: ");
            string server = Console.ReadLine();
            
            Console.Write("数据库名 (默认 CAMDB): ");
            string database = Console.ReadLine();
            if (string.IsNullOrEmpty(database)) database = "CAMDB";
            
            Console.Write("用户名: ");
            string username = Console.ReadLine();
            
            Console.Write("密码: ");
            string password = ReadPassword();
            
            Console.WriteLine();
            
            await TestMultipleConnectionStrings(server, database, username, password);
            
            // 如果有成功的连接，测试具体查询
            Console.WriteLine("是否测试您的补贴分析查询? (y/n): ");
            if (Console.ReadLine()?.ToLower() == "y")
            {
                string successfulConnectionString = $"Server={server};Database={database};User Id={username};Password={password};Encrypt=false;TrustServerCertificate=true;Connection Timeout=30;";
                await TestYourQuery(successfulConnectionString);
            }
        }

        /// <summary>
        /// 安全读取密码
        /// </summary>
        private static string ReadPassword()
        {
            string password = "";
            ConsoleKeyInfo key;
            do
            {
                key = Console.ReadKey(true);
                if (key.Key != ConsoleKey.Backspace && key.Key != ConsoleKey.Enter)
                {
                    password += key.KeyChar;
                    Console.Write("*");
                }
                else if (key.Key == ConsoleKey.Backspace && password.Length > 0)
                {
                    password = password.Substring(0, password.Length - 1);
                    Console.Write("\b \b");
                }
            }
            while (key.Key != ConsoleKey.Enter);
            return password;
        }
    }
}
