<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-28T10:00:00.000Z" agent="DrawIO-Designer" version="21.1.2">
  <diagram name="城市外卖运营流程图" id="city-delivery-operation-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- 标题 -->
        <mxCell id="title" value="城市外卖运营完整流程图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#212121;" vertex="1" parent="1">
          <mxGeometry x="650" y="20" width="300" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 用户端流程区域 -->
        <mxCell id="user-area" value="用户端流程" style="swimlane;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#2196F3;strokeWidth=2;fontSize=14;fontStyle=1;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="300" height="400" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-1" value="用户打开APP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="20" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-2" value="浏览商家/商品" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="160" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-3" value="选择商品\n加购物车" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="160" y="90" width="120" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-4" value="确认订单信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="20" y="90" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-5" value="选择支付方式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="20" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-6" value="支付成功" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="160" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-7" value="等待商家确认" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="20" y="190" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-8" value="实时跟踪配送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="160" y="190" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-9" value="收到外卖" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="20" y="240" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="user-10" value="订单评价" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1976D2;fontColor=white;fontSize=12;" vertex="1" parent="user-area">
          <mxGeometry x="160" y="240" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <!-- 商家端流程区域 -->
        <mxCell id="merchant-area" value="商家端流程" style="swimlane;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;fontSize=14;fontStyle=1;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="400" y="80" width="300" height="400" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-1" value="接收订单通知" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="20" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-2" value="确认订单" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="160" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-3" value="开始备餐" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="20" y="90" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-4" value="餐品制作完成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="160" y="90" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-5" value="打包餐品" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="20" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-6" value="等待骑手取餐" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="160" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-7" value="交付给骑手" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="20" y="190" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="merchant-8" value="订单完成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="merchant-area">
          <mxGeometry x="160" y="190" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <!-- 配送端流程区域 -->
        <mxCell id="delivery-area" value="配送端流程" style="swimlane;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;fontSize=14;fontStyle=1;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="750" y="80" width="300" height="400" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-1" value="接收配送任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="20" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-2" value="前往商家取餐" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="160" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-3" value="到达商家" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="20" y="90" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-4" value="取餐确认" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="160" y="90" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-5" value="前往用户地址" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="20" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-6" value="联系用户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#F57C00;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="160" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-7" value="送达用户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="20" y="190" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-8" value="确认收货" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="160" y="190" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="delivery-9" value="配送完成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#388E3C;fontColor=white;fontSize=12;" vertex="1" parent="delivery-area">
          <mxGeometry x="90" y="240" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <!-- 平台运营管理区域 -->
        <mxCell id="platform-area" value="平台运营管理" style="swimlane;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=2;fontSize=14;fontStyle=1;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="1100" y="80" width="300" height="400" as="geometry"/>
        </mxCell>
        
        <mxCell id="platform-1" value="订单生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9C27B0;strokeColor=#7B1FA2;fontColor=white;fontSize=12;" vertex="1" parent="platform-area">
          <mxGeometry x="20" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="platform-2" value="智能调度系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9C27B0;strokeColor=#7B1FA2;fontColor=white;fontSize=12;" vertex="1" parent="platform-area">
          <mxGeometry x="160" y="40" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="platform-3" value="骑手分配" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9C27B0;strokeColor=#7B1FA2;fontColor=white;fontSize=12;" vertex="1" parent="platform-area">
          <mxGeometry x="20" y="90" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="platform-4" value="实时监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9C27B0;strokeColor=#7B1FA2;fontColor=white;fontSize=12;" vertex="1" parent="platform-area">
          <mxGeometry x="160" y="90" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="platform-5" value="异常处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=#D32F2F;fontColor=white;fontSize=12;" vertex="1" parent="platform-area">
          <mxGeometry x="20" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="platform-6" value="数据分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9C27B0;strokeColor=#7B1FA2;fontColor=white;fontSize=12;" vertex="1" parent="platform-area">
          <mxGeometry x="160" y="140" width="120" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="platform-7" value="运营优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9C27B0;strokeColor=#7B1FA2;fontColor=white;fontSize=12;" vertex="1" parent="platform-area">
          <mxGeometry x="90" y="190" width="120" height="30" as="geometry"/>
        </mxCell>

        <!-- 补贴管理系统区域 -->
        <mxCell id="subsidy-area" value="补贴管理系统" style="swimlane;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=2;fontSize=14;fontStyle=1;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="520" width="500" height="200" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-1" value="活动配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=#D32F2F;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="20" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-2" value="补贴计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=#D32F2F;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="140" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-3" value="代理商补贴" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=#D32F2F;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="260" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-4" value="商家补贴" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=#D32F2F;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="380" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-5" value="用户优惠" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=#D32F2F;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="20" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-6" value="补贴结算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=#D32F2F;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="140" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-7" value="单均代补计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFC107;strokeColor=#FF8F00;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="260" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-8" value="代补占比分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFC107;strokeColor=#FF8F00;fontColor=white;fontSize=12;" vertex="1" parent="subsidy-area">
          <mxGeometry x="380" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <!-- 数据分析系统区域 -->
        <mxCell id="data-area" value="数据分析系统" style="swimlane;whiteSpace=wrap;html=1;fillColor=#E0F2F1;strokeColor=#009688;strokeWidth=2;fontSize=14;fontStyle=1;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="600" y="520" width="500" height="200" as="geometry"/>
        </mxCell>

        <mxCell id="data-1" value="订单数据收集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="20" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="data-2" value="用户行为分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="140" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="data-3" value="商家运营分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="260" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="data-4" value="配送效率分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="380" y="40" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="data-5" value="补贴效果分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="20" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="data-6" value="运营决策支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="140" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="data-7" value="ROI分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="260" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <mxCell id="data-8" value="预测模型" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695C;fontColor=white;fontSize=12;" vertex="1" parent="data-area">
          <mxGeometry x="380" y="90" width="100" height="30" as="geometry"/>
        </mxCell>

        <!-- 用户端内部连接线 -->
        <mxCell id="user-flow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-1" target="user-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-2" target="user-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-3" target="user-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-4" target="user-5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-5" target="user-6">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-6" target="user-7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-7" target="user-8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-8" target="user-9">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="user-flow-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;" edge="1" parent="1" source="user-9" target="user-10">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 商家端内部连接线 -->
        <mxCell id="merchant-flow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="merchant-1" target="merchant-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="merchant-flow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="merchant-2" target="merchant-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="merchant-flow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="merchant-3" target="merchant-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="merchant-flow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="merchant-4" target="merchant-5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="merchant-flow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="merchant-5" target="merchant-6">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="merchant-flow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="merchant-6" target="merchant-7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="merchant-flow-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1" source="merchant-7" target="merchant-8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 配送端内部连接线 -->
        <mxCell id="delivery-flow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-1" target="delivery-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="delivery-flow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-2" target="delivery-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="delivery-flow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-3" target="delivery-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="delivery-flow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-4" target="delivery-5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="delivery-flow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-5" target="delivery-6">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="delivery-flow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-6" target="delivery-7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="delivery-flow-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-7" target="delivery-8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="delivery-flow-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1" source="delivery-8" target="delivery-9">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 平台端内部连接线 -->
        <mxCell id="platform-flow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9C27B0;strokeWidth=2;" edge="1" parent="1" source="platform-1" target="platform-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="platform-flow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9C27B0;strokeWidth=2;" edge="1" parent="1" source="platform-2" target="platform-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="platform-flow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9C27B0;strokeWidth=2;" edge="1" parent="1" source="platform-3" target="platform-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="platform-flow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9C27B0;strokeWidth=2;" edge="1" parent="1" source="platform-4" target="platform-5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="platform-flow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9C27B0;strokeWidth=2;" edge="1" parent="1" source="platform-4" target="platform-6">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="platform-flow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9C27B0;strokeWidth=2;" edge="1" parent="1" source="platform-6" target="platform-7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 跨系统连接线 -->
        <!-- 用户支付成功 -> 商家接收订单 -->
        <mxCell id="cross-flow-1" value="订单传递" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF5722;strokeWidth=3;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="user-6" target="merchant-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 商家确认订单 -> 平台订单生成 -->
        <mxCell id="cross-flow-2" value="订单确认" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF5722;strokeWidth=3;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="merchant-2" target="platform-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 平台骑手分配 -> 配送接收任务 -->
        <mxCell id="cross-flow-3" value="任务分配" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF5722;strokeWidth=3;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="platform-3" target="delivery-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 商家交付骑手 -> 配送取餐确认 -->
        <mxCell id="cross-flow-4" value="取餐交接" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF5722;strokeWidth=3;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="merchant-7" target="delivery-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 配送确认收货 -> 用户收到外卖 -->
        <mxCell id="cross-flow-5" value="送达确认" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF5722;strokeWidth=3;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="delivery-8" target="user-9">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 用户确认订单 -> 补贴计算 -->
        <mxCell id="cross-flow-6" value="触发补贴" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#673AB7;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="user-4" target="subsidy-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 商家确认订单 -> 补贴计算 -->
        <mxCell id="cross-flow-7" value="商家补贴" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#673AB7;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="merchant-2" target="subsidy-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 补贴结算 -> 商家订单完成 -->
        <mxCell id="cross-flow-8" value="补贴到账" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#673AB7;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="subsidy-6" target="merchant-8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 用户评价 -> 数据收集 -->
        <mxCell id="cross-flow-9" value="数据收集" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#795548;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="user-10" target="data-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 商家订单完成 -> 数据收集 -->
        <mxCell id="cross-flow-10" value="商家数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#795548;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="merchant-8" target="data-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 配送完成 -> 数据收集 -->
        <mxCell id="cross-flow-11" value="配送数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#795548;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="delivery-9" target="data-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 数据分析决策支持 -> 平台运营优化 -->
        <mxCell id="cross-flow-12" value="决策支持" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#795548;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="data-6" target="platform-7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 数据分析 -> 活动配置 -->
        <mxCell id="cross-flow-13" value="优化建议" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#795548;strokeWidth=2;labelBackgroundColor=white;fontSize=10;" edge="1" parent="1" source="data-5" target="subsidy-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 补贴管理内部连接线 -->
        <mxCell id="subsidy-flow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#F44336;strokeWidth=2;" edge="1" parent="1" source="subsidy-1" target="subsidy-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-flow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#F44336;strokeWidth=2;" edge="1" parent="1" source="subsidy-2" target="subsidy-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-flow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#F44336;strokeWidth=2;" edge="1" parent="1" source="subsidy-3" target="subsidy-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-flow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#F44336;strokeWidth=2;" edge="1" parent="1" source="subsidy-2" target="subsidy-5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-flow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#F44336;strokeWidth=2;" edge="1" parent="1" source="subsidy-5" target="subsidy-6">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-flow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FFC107;strokeWidth=2;" edge="1" parent="1" source="subsidy-6" target="subsidy-7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="subsidy-flow-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FFC107;strokeWidth=2;" edge="1" parent="1" source="subsidy-7" target="subsidy-8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 数据分析内部连接线 -->
        <mxCell id="data-flow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#009688;strokeWidth=2;" edge="1" parent="1" source="data-1" target="data-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="data-flow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#009688;strokeWidth=2;" edge="1" parent="1" source="data-2" target="data-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="data-flow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#009688;strokeWidth=2;" edge="1" parent="1" source="data-3" target="data-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="data-flow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#009688;strokeWidth=2;" edge="1" parent="1" source="data-1" target="data-5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="data-flow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#009688;strokeWidth=2;" edge="1" parent="1" source="data-5" target="data-6">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="data-flow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#009688;strokeWidth=2;" edge="1" parent="1" source="data-6" target="data-7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="data-flow-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#009688;strokeWidth=2;" edge="1" parent="1" source="data-7" target="data-8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
