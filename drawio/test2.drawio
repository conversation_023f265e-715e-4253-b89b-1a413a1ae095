<mxfile host="app.diagrams.net" modified="2024-01-15T10:00:00.000Z" agent="DrawIO-Generator" etag="abc123" version="22.1.11">
  <diagram name="游戏出行用户体验地图" id="user-journey-map">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- 标题 -->
        <mxCell id="title" value="关于游戏出行的用户体验地图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="20" width="300" height="30" as="geometry"/>
        </mxCell>
        
        <!-- 阶段标签 -->
        <mxCell id="stage_label" value="阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E6E6FA;strokeColor=#9673A6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="80" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 主要阶段流程 -->
        <mxCell id="stage1" value="行程规划" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D4EDDA;strokeColor=#28A745;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="80" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="stage2" value="乘坐公共交通" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6C757D;strokeColor=#495057;fontSize=12;fontStyle=1;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="310" y="80" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="stage3" value="取票进站" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6C757D;strokeColor=#495057;fontSize=12;fontStyle=1;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="470" y="80" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="stage4" value="到达" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6C757D;strokeColor=#495057;fontSize=12;fontStyle=1;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="630" y="80" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="stage5" value="入住酒店" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6C757D;strokeColor=#495057;fontSize=12;fontStyle=1;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="790" y="80" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 箭头连接 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="stage1" target="stage2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="100" as="sourcePoint"/>
            <mxPoint x="310" y="100" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="stage2" target="stage3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="100" as="sourcePoint"/>
            <mxPoint x="470" y="100" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="stage3" target="stage4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="610" y="100" as="sourcePoint"/>
            <mxPoint x="630" y="100" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="stage4" target="stage5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="770" y="100" as="sourcePoint"/>
            <mxPoint x="790" y="100" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <!-- 行为标签 -->
        <mxCell id="behavior_label" value="行为" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E6E6FA;strokeColor=#9673A6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="160" width="80" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 行为描述 -->
        <mxCell id="behavior1" value="协调假期&#xa;规划路线&#xa;预定酒店&#xa;退订某些购买车票" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;fontSize=10;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="150" y="160" width="140" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="behavior2" value="检查车身情况&#xa;组织财车&#xa;用手机看音乐" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;fontSize=10;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="310" y="160" width="140" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="behavior3" value="排队进站&#xa;候车&#xa;使用洗手间" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;fontSize=10;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="470" y="160" width="140" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="behavior4" value="查找出口&#xa;打车&#xa;等来出租司机" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;fontSize=10;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="630" y="160" width="140" height="60" as="geometry"/>
        </mxCell>
        
        <mxCell id="behavior5" value="入住登记&#xa;选择房间&#xa;入住" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;fontSize=10;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="790" y="160" width="140" height="60" as="geometry"/>
        </mxCell>
        
        <!-- 痛点标签 -->
        <mxCell id="painpoint_label" value="痛点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E6E6FA;strokeColor=#9673A6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="250" width="80" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 痛点描述 -->
        <mxCell id="pain1" value="去看哪里，在久坐&#xa;在询问同，太久如何" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFF3CD;strokeColor=#FFEAA7;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="150" y="250" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="pain2" value="明确等车的方更好&#xa;会不会谈论" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFF3CD;strokeColor=#FFEAA7;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="310" y="250" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="pain3" value="进站人多不多&#xa;卫生间在管理&#xa;都发大厅有没有位" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFF3CD;strokeColor=#FFEAA7;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="470" y="250" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="pain4" value="几点到站&#xa;当地好不好打车&#xa;来接什么交通工具" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFF3CD;strokeColor=#FFEAA7;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="630" y="250" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="pain5" value="房间有没有网络&#xa;酒店位置不提供早餐&#xa;房间交通方不方便" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFF3CD;strokeColor=#FFEAA7;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="790" y="250" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <!-- 情绪标签 -->
        <mxCell id="emotion_label" value="情绪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E6E6FA;strokeColor=#9673A6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="340" width="80" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 情绪曲线和表情 -->
        <mxCell id="emotion_curve" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=3;strokeColor=#FFA500;fillColor=#FFE5B4;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="150" y="400" as="sourcePoint"/>
            <mxPoint x="930" y="440" as="targetPoint"/>
            <Array as="points">
              <mxPoint x="200" y="380"/>
              <mxPoint x="270" y="360"/>
              <mxPoint x="350" y="390"/>
              <mxPoint x="420" y="370"/>
              <mxPoint x="540" y="390"/>
              <mxPoint x="650" y="410"/>
              <mxPoint x="780" y="430"/>
              <mxPoint x="860" y="440"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 情绪表情符号 -->
        <mxCell id="emoji1" value="😊" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="190" y="370" width="20" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="emoji2" value="😐" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="340" y="380" width="20" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="emoji3" value="😟" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="530" y="380" width="20" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="emoji4" value="😊" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="640" y="400" width="20" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="emoji5" value="😔" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="850" y="430" width="20" height="20" as="geometry"/>
        </mxCell>
        
        <!-- 情绪文字描述 -->
        <mxCell id="emotion_text1" value="看看怎么去方便" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="165" y="395" width="70" height="15" as="geometry"/>
        </mxCell>
        
        <mxCell id="emotion_text2" value="今天上班太累了" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="320" y="410" width="70" height="15" as="geometry"/>
        </mxCell>
        
        <mxCell id="emotion_text3" value="排队时间太长了" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="505" y="410" width="70" height="15" as="geometry"/>
        </mxCell>
        
        <mxCell id="emotion_text4" value="打车太贵了" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="620" y="430" width="60" height="15" as="geometry"/>
        </mxCell>
        
        <mxCell id="emotion_text5" value="酒店房间网上网络不好" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="805" y="460" width="90" height="15" as="geometry"/>
        </mxCell>
        
        <!-- 需求点标签 -->
        <mxCell id="needs_label" value="需求机会点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E6E6FA;strokeColor=#9673A6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="500" width="80" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 需求点描述 -->
        <mxCell id="need1" value="不知道从哪获取真实可靠出款" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="150" y="500" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="need2" value="路况提供商" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="310" y="500" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="need3" value="入体验，排队时间长" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="470" y="500" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="need4" value="打车实时价格与系统不符合，&#xa;司机乱加价" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="630" y="500" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="need5" value="酒店环境没法保证" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="790" y="500" width="140" height="40" as="geometry"/>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>