<mxfile host="65bd71144e">
    <diagram name="Page-1" id="page1">
        <mxGraphModel dx="927" dy="829" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0" adaptiveColors="simple">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="cmp Application-architecture-design-framework" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="20" y="20" width="400" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="reference_container" value="应用系统参考" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;verticalAlign=top;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="50" y="70" width="200" height="480" as="geometry"/>
                </mxCell>
                <mxCell id="industry_model" value="行业参考模型" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="75" y="180" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="enterprise_assets" value="企业应用架构资产" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="75" y="370" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="core_design_container" value="应用核心设计" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;verticalAlign=top;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="270" y="70" width="730" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="application" value="应用" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="290" y="120" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="domain_service" value="领域服务" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="470" y="120" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="function_interface" value="功能接口" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="650" y="120" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="product_solution" value="产品与解决方案" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="830" y="120" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="system_design_container" value="应用系统设计" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;verticalAlign=top;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="270" y="240" width="730" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="presentation_layer" value="表现层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="290" y="290" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="application_layer" value="应用层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="470" y="290" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="domain_layer" value="领域层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="650" y="290" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="infrastructure_layer" value="基础设施层" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="830" y="290" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="architecture_mgmt_container" value="应用架构管理" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;verticalAlign=top;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="270" y="410" width="730" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="app_sharing" value="应用共享" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="290" y="470" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="app_development" value="应用开发" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="530" y="470" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="app_integration" value="应用集成" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="770" y="470" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="quality_standards_container" value="应用系统质测规范" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;verticalAlign=top;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="1020" y="70" width="180" height="480" as="geometry"/>
                </mxCell>
                <mxCell id="service_design_principle" value="服务设计原则" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="1045" y="130" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="service_layer_principle" value="服务分层原则" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="1045" y="190" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="design_tools" value="设计工具" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="1045" y="250" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="design_standards" value="设计规范" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="1045" y="310" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="dev_standards" value="开发规范" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="1045" y="370" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="integration_principle" value="系统集成原则" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
                    <mxGeometry x="1045" y="430" width="130" height="40" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>