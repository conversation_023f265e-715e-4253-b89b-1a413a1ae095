<mxfile host="app.diagrams.net" modified="2024-04-12T08:00:00.000Z" agent="Gemini-Code-Assistant" version="24.2.2" etag="auto">
  <diagram name="Replicated Architecture Diagram" id="unique-id-12345">
    <mxGraphModel dx="1462" dy="894" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="1700" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- Layer Definitions -->
        <!-- y_pos starts at 40 -->

        <!-- Layer 1: 接入终端 (y=40, height=100) -->
        <mxCell id="layer1_label" value="接入终端" style="shape=chevron;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#F5F5F5;strokeColor=#BDBDBD;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="40" y="60" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer1_container" value="" style="shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="40" width="1000" height="100" as="geometry" />
        </mxCell>
        <!-- Components for Layer 1 -->
        <mxCell id="comp1_1" value="PC端" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="200" y="65" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp1_2" value="APP" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="340" y="65" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp1_3" value="小程序" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="480" y="65" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp1_4" value="API开放平台" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="620" y="65" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp1_5" value="其他接入方式" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="760" y="65" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Layer 2: API接口层 (y=180, height=100) -->
        <mxCell id="layer2_label" value="API接口层" style="shape=chevron;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#F5F5F5;strokeColor=#BDBDBD;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="40" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer2_container" value="" style="shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#FFE082;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="180" width="1000" height="100" as="geometry" />
        </mxCell>
        <!-- Components for Layer 2 -->
        <mxCell id="comp2_1" value="API网关" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="200" y="205" width="480" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp2_2" value="负载均衡" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="700" y="205" width="480" height="50" as="geometry" />
        </mxCell>

        <!-- Layer 3: 业务服务层 (y=320, height=160) -->
        <mxCell id="layer3_label" value="业务服务层" style="shape=chevron;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#F5F5F5;strokeColor=#BDBDBD;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="40" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer3_container" value="" style="shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#F8BBD0;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="320" width="800" height="160" as="geometry" />
        </mxCell>
        <!-- Components for Layer 3 -->
        <mxCell id="comp3_1" value="用户中心" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="200" y="340" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp3_2" value="财务中心" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="360" y="340" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp3_3" value="渠道中心" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="520" y="340" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp3_4" value="监管中心" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="680" y="340" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp3_5" value="运维中心" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="200" y="410" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp3_6" value="运营中心" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="360" y="410" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp3_7" value="基于框架开发的业务模块" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#BBDEFB;strokeColor=#64B5F6;" vertex="1" parent="1">
          <mxGeometry x="520" y="410" width="300" height="50" as="geometry" />
        </mxCell>

        <!-- Layer 4: 基础服务层 (y=520, height=220) -->
        <mxCell id="layer4_label" value="基础服务层" style="shape=chevron;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#F5F5F5;strokeColor=#BDBDBD;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="40" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer4_container" value="" style="shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;html=1;fillColor=#E8F5E9;strokeColor=#A5D6A7;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="520" width="800" height="220" as="geometry" />
        </mxCell>
        <!-- Components for Layer 4 -->
        <mxCell id="comp4_1" value="系统设置" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="200" y="540" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_2" value="地区设置" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="340" y="540" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_3" value="支付插件" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="480" y="540" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_4" value="存储插件" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="620" y="540" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_5" value="审计日志" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="200" y="610" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_6" value="通信插件" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="340" y="610" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_7" value="其他服务模块" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#BBDEFB;strokeColor=#64B5F6;" vertex="1" parent="1">
          <mxGeometry x="480" y="610" width="260" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_8" value="工具类" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="800" y="540" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp4_9" value="辅助功能" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="800" y="610" width="160" height="50" as="geometry" />
        </mxCell>

        <!-- Layer 5: 数据层 (y=780, height=100) -->
        <mxCell id="layer5_label" value="数据层" style="shape=chevron;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#F5F5F5;strokeColor=#BDBDBD;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="40" y="800" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer5_container" value="" style="shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#FFE082;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="780" width="1000" height="100" as="geometry" />
        </mxCell>
        <!-- Components for Layer 5 -->
        <mxCell id="comp5_1" value="数据缓存" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="200" y="805" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp5_2" value="事务" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="340" y="805" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp5_3" value="读写数据库" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="480" y="805" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp5_4" value="缓存过期控制" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="620" y="805" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp5_5" value="文件读写" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="760" y="805" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp5_6" value="数据同步" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="900" y="805" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Layer 6: 数据库 (y=920, height=140) -->
        <mxCell id="layer6_label" value="数据库" style="shape=chevron;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#F5F5F5;strokeColor=#BDBDBD;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="40" y="960" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer6_container" value="" style="shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="920" width="1000" height="140" as="geometry" />
        </mxCell>
        <!-- Components for Layer 6 -->
        <mxCell id="db1" value="MYSQL" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="240" y="945" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="db2" value="Redis" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="500" y="945" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="db3" value="ES搜索" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="760" y="945" width="160" height="90" as="geometry" />
        </mxCell>

        <!-- Layer 7: 运行环境 (y=1100, height=100) -->
        <mxCell id="layer7_label" value="运行环境" style="shape=chevron;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#F5F5F5;strokeColor=#BDBDBD;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="40" y="1120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer7_container" value="" style="shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="1100" width="1000" height="100" as="geometry" />
        </mxCell>
        <!-- Components for Layer 7 -->
        <mxCell id="comp7_1" value="云主机" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="380" y="1125" width="300" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comp7_2" value="独立服务器" style="shape=rect;rounded=1;arcSize=4;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;fontColor=#424242;fillColor=#FFFFFF;strokeColor=#BDBDBD;" vertex="1" parent="1">
          <mxGeometry x="700" y="1125" width="300" height="50" as="geometry" />
        </mxCell>

        <!-- Vertical Bars -->
        <mxCell id="log_bar" value="&lt;div style=&quot;writing-mode: vertical-rl; text-orientation: mixed; white-space: nowrap;&quot;&gt;日 志 记 录&lt;/div&gt;" style="html=1;shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;fontFamily=Helvetica;fontSize=14;fontStyle=1;fontColor=#212121;fillColor=#C8E6C9;strokeColor=#81C784;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="1000" y="400" width="60" height="240" as="geometry" />
        </mxCell>
        <mxCell id="auth_bar" value="&lt;div style=&quot;writing-mode: vertical-rl; text-orientation: mixed; white-space: nowrap; line-height: 1.2; letter-spacing: 0.1em;&quot;&gt;统 一 权 限 控 制&lt;/div&gt;" style="html=1;shape=rect;rounded=1;arcSize=8;whiteSpace=wrap;fontFamily=Helvetica;fontSize=18;fontStyle=1;fontColor=#212121;fillColor=#FFCDD2;strokeColor=#E57373;align=center;verticalAlign=middle;" vertex="1" parent="1">
          <mxGeometry x="1200" y="40" width="80" height="1160" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 