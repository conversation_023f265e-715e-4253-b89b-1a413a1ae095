<mxfile host="65bd71144e">
    <diagram name="TodoList Architecture" id="diagram-1">
        <mxGraphModel dx="923" dy="911" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="智能待办事项系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#212121;" parent="1" vertex="1">
                    <mxGeometry x="434.5" y="30" width="300" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="frontend-container" value="" style="rounded=1;arcSize=3;whiteSpace=wrap;html=1;align=center;verticalAlign=top;fillColor=#F5F5F5;strokeColor=#E0E0E0;dashed=1;" vertex="1" parent="1">
                    <mxGeometry x="184.5" y="100" width="800" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="frontend-title" value="前端层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#212121;" vertex="1" parent="1">
                    <mxGeometry x="204.5" y="110" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="web-client" value="Web 客户端&#xa;React + TypeScript&#xa;- Ant Design 组件库&#xa;- Redux 状态管理&#xa;- React Router 路由" style="rounded=1;arcSize=8;whiteSpace=wrap;html=1;align=left;verticalAlign=middle;fillColor=#C7FF0F;strokeColor=#2196F3;spacingLeft=8;" vertex="1" parent="1">
                    <mxGeometry x="214.5" y="150" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-client" value="移动端&#xa;React Native&#xa;- iOS/Android 原生组件&#xa;- MobX 状态管理&#xa;- 离线存储支持" style="rounded=1;arcSize=8;whiteSpace=wrap;html=1;align=left;verticalAlign=middle;fillColor=#E3F2FD;strokeColor=#2196F3;spacingLeft=8;" vertex="1" parent="1">
                    <mxGeometry x="494.5" y="150" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="desktop-client" value="桌面端&#xa;Electron&#xa;- 系统托盘集成&#xa;- 本地通知&#xa;- 快捷键支持" style="rounded=1;arcSize=8;whiteSpace=wrap;html=1;align=left;verticalAlign=middle;fillColor=#E3F2FD;strokeColor=#2196F3;spacingLeft=8;" vertex="1" parent="1">
                    <mxGeometry x="774.5" y="150" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="gateway-container" value="" style="rounded=1;arcSize=3;whiteSpace=wrap;html=1;align=center;verticalAlign=top;fillColor=#F5F5F5;strokeColor=#E0E0E0;dashed=1;" vertex="1" parent="1">
                    <mxGeometry x="184.5" y="270" width="800" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="gateway-title" value="API 网关层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#212121;" vertex="1" parent="1">
                    <mxGeometry x="204.5" y="280" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="gateway" value="Nginx API 网关&lt;br&gt;- 请求路由&lt;br&gt;- 负载均衡&lt;br&gt;- 安全过滤&lt;br&gt;- 限流控制" style="rounded=1;arcSize=8;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;fillColor=#FFF3E0;strokeColor=#FFC107;spacingLeft=8;" vertex="1" parent="1">
                    <mxGeometry x="490" y="280" width="194.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="services-container" value="" style="rounded=1;arcSize=3;whiteSpace=wrap;html=1;align=center;verticalAlign=top;fillColor=#F5F5F5;strokeColor=#E0E0E0;dashed=1;" vertex="1" parent="1">
                    <mxGeometry x="184.5" y="400" width="800" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="services-title" value="微服务层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#212121;" vertex="1" parent="1">
                    <mxGeometry x="204.5" y="410" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="auth-service" value="认证服务&#xa;- JWT 令牌管理&#xa;- OAuth2.0 集成&#xa;- 权限控制&#xa;- 会话管理" style="rounded=1;arcSize=4;whiteSpace=wrap;html=1;align=left;verticalAlign=middle;fillColor=#FFFFFF;strokeColor=#2196F3;spacingLeft=8;" parent="1" vertex="1">
                    <mxGeometry x="214.5" y="450" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="task-service" value="任务服务&#xa;- 任务 CRUD&#xa;- 标签管理&#xa;- 优先级排序&#xa;- 定时提醒" style="rounded=1;arcSize=4;whiteSpace=wrap;html=1;align=left;verticalAlign=middle;fillColor=#FFFFFF;strokeColor=#2196F3;spacingLeft=8;" parent="1" vertex="1">
                    <mxGeometry x="404.5" y="450" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="user-service" value="用户服务&#xa;- 用户管理&#xa;- 个人设置&#xa;- 偏好配置&#xa;- 数据同步" style="rounded=1;arcSize=4;whiteSpace=wrap;html=1;align=left;verticalAlign=middle;fillColor=#FFFFFF;strokeColor=#2196F3;spacingLeft=8;" parent="1" vertex="1">
                    <mxGeometry x="594.5" y="450" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="notification-service" value="通知服务&#xa;- 邮件通知&#xa;- 短信提醒&#xa;- 推送服务&#xa;- 订阅管理" style="rounded=1;arcSize=4;whiteSpace=wrap;html=1;align=left;verticalAlign=middle;fillColor=#FFFFFF;strokeColor=#2196F3;spacingLeft=8;" vertex="1" parent="1">
                    <mxGeometry x="784.5" y="450" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="3" style="edgeStyle=none;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="data-container">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="984.7777777777778" y="750.1111111111111" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="data-container" value="" style="rounded=1;arcSize=3;whiteSpace=wrap;html=1;align=center;verticalAlign=top;fillColor=#F5F5F5;strokeColor=#E0E0E0;dashed=1;" vertex="1" parent="1">
                    <mxGeometry x="184.5" y="590" width="800" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="data-title" value="数据层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#212121;" vertex="1" parent="1">
                    <mxGeometry x="204.5" y="600" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="main-db" value="主数据库&#xa;PostgreSQL" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=center;verticalAlign=top;fillColor=#E8F5E9;strokeColor=#4CAF50;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="214.5" y="640" width="175.5" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="main-db-text" value="- 用户数据&#xa;- 任务数据" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="267.5" y="709" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cache" value="缓存服务&#xa;Redis" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=center;verticalAlign=top;fillColor=#E8F5E9;strokeColor=#4CAF50;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="404.5" y="640" width="165.5" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="cache-text" value="- 会话缓存&#xa;- 热点数据" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="455" y="710" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="queue" value="消息队列&#xa;RabbitMQ" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=center;verticalAlign=top;fillColor=#E8F5E9;strokeColor=#4CAF50;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="594.5" y="640" width="165.5" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="queue-text" value="- 异步任务&#xa;- 通知分发" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="645.5" y="710" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="search" value="搜索引擎&#xa;Elasticsearch" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=center;verticalAlign=top;fillColor=#E8F5E9;strokeColor=#4CAF50;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="784.5" y="640" width="175.5" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="search-text" value="- 全文检索&#xa;- 数据分析" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="840" y="710" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="frontend-gateway" value="HTTPS / WebSocket" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#757575;" edge="1" parent="1" source="frontend-container" target="gateway-container">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="gateway-services" value="gRPC / REST" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#757575;" edge="1" parent="1" source="gateway-container" target="services-container">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="services-data" value="数据访问" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#757575;" edge="1" parent="1" source="services-container" target="data-container">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>