<mxfile host="app.diagrams.net" agent="Gemini AI" version="21.1.2" modified="2024-07-29T12:00:00Z">
    <diagram name="政府经济报告Agent架构" id="main-diagram">
        <mxGraphModel dx="1800" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>

                <!-- Title -->
                <mxCell id="title-2" value="政府经济报告生成 Agent 架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#212121;" vertex="1" parent="1">
                    <mxGeometry x="450" y="20" width="280" height="30" as="geometry"/>
                </mxCell>

                <!-- Data Ingestion Flow -->
                <mxCell id="data-sources-group" value="数据源" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#212121;" vertex="1" parent="1">
                    <mxGeometry x="40" y="70" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="ds_wechat-3" value="公众号" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#4CAF50;fontColor=#FFFFFF;strokeColor=#388E3C;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="40" y="100" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="ds_douyin-4" value="抖音" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#4CAF50;fontColor=#FFFFFF;strokeColor=#388E3C;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="40" y="150" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="ds_gov-5" value="政府网站" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#4CAF50;fontColor=#FFFFFF;strokeColor=#388E3C;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="40" y="200" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="ds_other-6" value="其他信息源" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#4CAF50;fontColor=#FFFFFF;strokeColor=#388E3C;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="40" y="250" width="100" height="40" as="geometry"/>
                </mxCell>

                <mxCell id="crawler-7" value="网络爬虫系统" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#2196F3;fontColor=#FFFFFF;strokeColor=#1E88E5;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="220" y="175" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_ds_crawler_1-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="ds_wechat-3" target="crawler-7"/>
                <mxCell id="arrow_ds_crawler_2-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="ds_douyin-4" target="crawler-7"/>
                <mxCell id="arrow_ds_crawler_3-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="ds_gov-5" target="crawler-7"/>
                <mxCell id="arrow_ds_crawler_4-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="ds_other-6" target="crawler-7"/>

                <mxCell id="graphrag-12" value="GraphRAG&lt;br&gt;知识图谱构建" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#2196F3;fontColor=#FFFFFF;strokeColor=#1E88E5;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="420" y="175" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_crawler_graphrag-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="crawler-7" target="graphrag-12"/>

                <mxCell id="kg_db-14" value="知识图谱数据库" style="shape=cylinder;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#4CAF50;fontColor=#FFFFFF;strokeColor=#388E3C;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="620" y="170" width="120" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_graphrag_db-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="graphrag-12" target="kg_db-14"/>

                <!-- Generation Flow -->
                <mxCell id="user_query-16" value="用户输入问题" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#FFC107;fontColor=#212121;strokeColor=#FFA000;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="40" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
                
                <mxCell id="query_llm-17" value="查询分析大模型&lt;br&gt;(Query Analysis LLM)" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#2196F3;fontColor=#FFFFFF;strokeColor=#1E88E5;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="220" y="450" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_user_qllm-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="user_query-16" target="query_llm-17"/>

                <mxCell id="retrieval-19" value="知识检索" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#2196F3;fontColor=#FFFFFF;strokeColor=#1E88E5;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="480" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_qllm_retrieval-20" value="生成检索查询" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;fontSize=10;" edge="1" parent="1" source="query_llm-17" target="retrieval-19"/>

                <mxCell id="arrow_db_retrieval-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;entryX=0.5;entryY=1;exitX=0.5;exitY=0;" edge="1" parent="1" source="kg_db-14" target="retrieval-19">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="680" y="320"/>
                            <mxPoint x="540" y="320"/>
                        </Array>
                    </mxGeometry>
                </mxCell>

                <mxCell id="writing_llm-22" value="写作大模型&lt;br&gt;(Writing LLM)" style="shape=rounded;whiteSpace=wrap;html=1;arcSize=8;fillColor=#2196F3;fontColor=#FFFFFF;strokeColor=#1E88E5;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="760" y="450" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_retrieval_wllm-23" value="检索到的资料" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;fontSize=10;" edge="1" parent="1" source="retrieval-19" target="writing_llm-22"/>

                <mxCell id="input_outline-24" value="文章大纲" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#FFC107;fontColor=#212121;strokeColor=#FFA000;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="640" y="550" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="input_reqs-25" value="章节要求" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#FFC107;fontColor=#212121;strokeColor=#FFA000;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="760" y="550" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_inputs_wllm_1-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="input_outline-24" target="writing_llm-22"/>
                <mxCell id="arrow_inputs_wllm_2-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;" edge="1" parent="1" source="input_reqs-25" target="writing_llm-22"/>

                <mxCell id="final_report-28" value="政府经济报告" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#4CAF50;fontColor=#FFFFFF;strokeColor=#388E3C;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="1000" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="arrow_wllm_report-29" value="逐步输出" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#424242;fontSize=10;" edge="1" parent="1" source="writing_llm-22" target="final_report-28"/>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>