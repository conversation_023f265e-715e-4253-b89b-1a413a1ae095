{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-27T03:51:26.726Z", "updatedAt": "2025-06-27T03:51:26.727Z", "resourceCount": 7}, "resources": [{"id": "financial-analyst", "source": "project", "protocol": "role", "name": "Financial Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/financial-analyst/financial-analyst.role.md", "metadata": {"createdAt": "2025-06-27T03:51:26.726Z", "updatedAt": "2025-06-27T03:51:26.726Z", "scannedAt": "2025-06-27T03:51:26.726Z"}}, {"id": "financial-analysis", "source": "project", "protocol": "thought", "name": "Financial Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/financial-analyst/thought/financial-analysis.thought.md", "metadata": {"createdAt": "2025-06-27T03:51:26.727Z", "updatedAt": "2025-06-27T03:51:26.727Z", "scannedAt": "2025-06-27T03:51:26.727Z"}}, {"id": "financial-workflow", "source": "project", "protocol": "execution", "name": "Financial Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/financial-analyst/execution/financial-workflow.execution.md", "metadata": {"createdAt": "2025-06-27T03:51:26.727Z", "updatedAt": "2025-06-27T03:51:26.727Z", "scannedAt": "2025-06-27T03:51:26.727Z"}}, {"id": "risk-management", "source": "project", "protocol": "execution", "name": "Risk Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/financial-analyst/execution/risk-management.execution.md", "metadata": {"createdAt": "2025-06-27T03:51:26.727Z", "updatedAt": "2025-06-27T03:51:26.727Z", "scannedAt": "2025-06-27T03:51:26.727Z"}}, {"id": "financial-markets", "source": "project", "protocol": "knowledge", "name": "Financial Markets 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/financial-analyst/knowledge/financial-markets.knowledge.md", "metadata": {"createdAt": "2025-06-27T03:51:26.727Z", "updatedAt": "2025-06-27T03:51:26.727Z", "scannedAt": "2025-06-27T03:51:26.727Z"}}, {"id": "investment-strategies", "source": "project", "protocol": "knowledge", "name": "Investment Strategies 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/financial-analyst/knowledge/investment-strategies.knowledge.md", "metadata": {"createdAt": "2025-06-27T03:51:26.727Z", "updatedAt": "2025-06-27T03:51:26.727Z", "scannedAt": "2025-06-27T03:51:26.727Z"}}, {"id": "stock-analysis", "source": "project", "protocol": "knowledge", "name": "Stock Analysis 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/financial-analyst/knowledge/stock-analysis.knowledge.md", "metadata": {"createdAt": "2025-06-27T03:51:26.727Z", "updatedAt": "2025-06-27T03:51:26.727Z", "scannedAt": "2025-06-27T03:51:26.727Z"}}], "stats": {"totalResources": 7, "byProtocol": {"role": 1, "thought": 1, "execution": 2, "knowledge": 3}, "bySource": {"project": 7}}}