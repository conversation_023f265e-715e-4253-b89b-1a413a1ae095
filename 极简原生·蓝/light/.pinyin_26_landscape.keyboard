"\u9884\u7F16\u8F91\u533A\u9AD8\u5EA6": 19
"\u5DE5\u5177\u680F\u9AD8\u5EA6": 35
"\u952E\u76D8\u533A\u9AD8\u5EA6": 161
"\u7F16\u7801\u5B57\u4F53\u5927\u5C0F": 0.88em
"\u6A2A\u6392\u5E8F\u53F7\u5B57\u4F53\u5927\u5C0F": 1.19em
"\u6A2A\u6392\u6587\u5B57\u5B57\u4F53\u5927\u5C0F": 1.19em
"\u6A2A\u6392\u6CE8\u91CA\u5B57\u4F53\u5927\u5C0F": 0.81em
"\u5C55\u5F00\u5E8F\u53F7\u5B57\u4F53\u5927\u5C0F": 1.125em
"\u5C55\u5F00\u6587\u5B57\u5B57\u4F53\u5927\u5C0F": 1.125em
"\u5C55\u5F00\u6CE8\u91CA\u5B57\u4F53\u5927\u5C0F": 0.75em
"\u7F16\u7801\u8272": 070707
"\u9996\u9009\u8272": 377DFA
"\u6B21\u9009\u8272": 070707
"\u952E\u76D8\u5E95\u8272": CFD3D8
"\u6309\u952E\u5B57\u7B26\u6B63\u5E38\u8272": 070707
"\u6309\u952E\u5B57\u7B26\u9AD8\u4EAE\u8272": 070707
"\u4E0A\u4E0B\u5212\u5B57\u7B26\u6B63\u5E38\u8272": A7AEB9
"\u4E0A\u4E0B\u5212\u5B57\u7B26\u9AD8\u4EAE\u8272": A7AEB9
"\u56DE\u8F66\u952E\u5B57\u7B26\u8272": FFFFFF
"\u6309\u4E0B\u6C14\u6CE1\u5B57\u7B26\u8272": FFFFFF
"\u6ED1\u52A8\u6C14\u6CE1\u5B57\u7B26\u8272": FFFFFF
"\u957F\u6309\u6C14\u6CE1\u5B57\u7B26\u8272": 7DACFF
"\u957F\u6309\u5B57\u7B26\u9AD8\u4EAE\u8272": FFFFFF
"\u72B6\u6001\u680F\u56FE\u6807\u7F29\u653E": 0.55
"\u6536\u8D77\u56FE\u6807":
  fontSize: 1em
  normalColor: 070707
  highlightColor: 070707
"\u5C55\u5F00\u5019\u9009\u5217\u8868\u80CC\u666F\u8272": E3E4E9
"\u5C55\u5F00\u5019\u9009\u529F\u80FD\u952E\u5B57\u7B26\u6837\u5F0F":
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
"\u5B57\u7B26\u952E\u80CC\u666F":
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
"\u529F\u80FD\u952E\u80CC\u666F":
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
"\u7A7A\u683C\u80CC\u666F":
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: FFFFFF
  cornerRadius: 6
  normalLowerEdgeColor: B3B3B3
  highlightLowerEdgeColor: B3B3B3
"\u56DE\u8F66\u952E\u80CC\u666F":
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: 3379f5
  highlightColor: 296bdf
  cornerRadius: 6
  normalLowerEdgeColor: 1d61d9
  highlightLowerEdgeColor: 296bdf
"\u4E2D\u82F1\u524D\u666F1":
  center:
    x: 0.38
    y: 0.73
  text: "\u4E2D"
  fontSize: 0.81em
  normalColor: 070707
  highlightColor: 070707
"\u4E2D\u82F1\u524D\u666F2":
  center:
    x: 0.65
    y: 0.97
  text: "/\u82F1"
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
"26\u952E\u5B57\u7B26":
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
"\u4E0A\u5212\u5B57\u7B26":
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
"\u4E0B\u5212\u5B57\u7B26":
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
"Shift\u524D\u666F":
  center:
    x: 0.5
    y: 0.5
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
"Backspace\u524D\u666F":
  center:
    x: 0.5
    y: 0.5
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
"sym\u524D\u666F":
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
"dot\u524D\u666F":
  center:
    x: 0.5
    y: 0.75
  fontSize: 1em
  normalColor: 070707
  highlightColor: 070707
"dot\u4E0A\u5212\u5B57\u7B26":
  center:
    x: 0.58
    y: 0.5
  fontSize: 0.94em
  normalColor: 070707
  highlightColor: 070707
"space\u524D\u666F":
  center:
    x: 0.5
    y: 0.55
  fontSize: 1.25em
  normalColor: 070707
  highlightColor: 070707
"space\u524D\u666F1":
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
"enter\u524D\u666F":
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u5217\u8868\u6587\u5B57\u6837\u5F0F":
  normalColor: 070707
  fontSize: 0.875em
"\u5217\u8868\u680F\u9AD8\u4EAE\u80CC\u666F":
  type: original
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  normalColor: FFFFFFBB
  cornerRadius: 6
"\u6570\u5B57\u952E\u524D\u666F":
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
"period\u952E\u524D\u666F":
  center:
    x: 0.5
    y: 0.75
  fontSize: 1.125em
  normalColor: 070707
  highlightColor: 070707
"equal\u524D\u666F":
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
"at\u952E\u524D\u666F":
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
alphabeticHintBackgroundStyle:
  normalImage:
    file: pop
    image: IMG1
  center:
    y: 0.5
  contentMode: scaleToFill
alphabeticHoldSymbolsBackgroundStyle:
  normalImage:
    file: long
    image: IMG1
  targetScale: 0.9
  center:
    y: 0.4
alphabeticHoldSymbolsSelectedStyle:
  normalImage:
    file: focus
    image: IMG1
  targetScale: 0.9
  center:
    y: 0.35
ButtonHintForeground:
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
ButtonSwipeUpHintForeground:
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
ButtonSwipeDownHintForeground:
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
ButtonHoldSymbolsOfqForeground:
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
preeditBackgroundStyle:
  type: original
  normalColor: CFD3D8
toolbarBackgroundStyle:
  type: original
  normalColor: CFD3D8
verticalCandidateBackgroundStyle:
  type: original
  normalColor: CFD3D8
keyboardBackgroundStyle:
  type: original
  normalColor: CFD3D8
animation:
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 1
  toScale: 0.8
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 0.8
  toScale: 1
preeditHeight: 19
toolbarHeight: 35
keyboardHeight: 161
preedit:
  insets:
    left: 10
    top: 2
  backgroundStyle: preeditBackgroundStyle
  foregroundStyle: preeditForegroundStyle
preeditForegroundStyle:
  textColor: 070707
  fontSize: 0.88em
toolbar:
  backgroundStyle: toolbarBackgroundStyle
  primaryButtonStyle: primaryButtonStyle
  secondaryButtonStyle:
  - toolbarButtonHideStyle
  - toolbarButton1Style
  - toolbarButton2Style
  - toolbarButton3Style
  - toolbarButton4Style
  - toolbarButton5Style
  - toolbarButton6Style
  horizontalCandidateStyle: horizontalCandidateStyle
  verticalCandidateStyle: verticalCandidateStyle
primaryButtonStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - primaryButtonForegroundStyle
  action:
    floatKeyboardType: floatconfig
toolbarButtonBackgroundStyle:
  normalColor: 00000000
  highlightColor: 00000000
primaryButtonForegroundStyle:
  normalImage:
    file: "\u8BBE\u7F6E"
    image: IMG1
  highlightImage:
    file: "\u8BBE\u7F6E"
    image: IMG1
  targetScale: 0.55
toolbarButtonHideStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButtonHideForegroundStyle
  action: dismissKeyboard
toolbarButtonHideForegroundStyle:
  normalImage:
    file: "\u6536\u8D77"
    image: IMG1
  highlightImage:
    file: "\u6536\u8D77"
    image: IMG1
  targetScale: 0.55
toolbarButton1Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton1ForegroundStyle
  action:
    shortcutCommand: '#showPasteboardView'
toolbarButton1ForegroundStyle:
  normalImage:
    file: "\u526A\u8D34"
    image: IMG1
  highlightImage:
    file: "\u526A\u8D34"
    image: IMG1
  targetScale: 0.55
toolbarButton2Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton2ForegroundStyle
  action:
    shortcutCommand: '#showPhraseView'
toolbarButton2ForegroundStyle:
  normalImage:
    file: "\u77ED\u8BED"
    image: IMG1
  highlightImage:
    file: "\u77ED\u8BED"
    image: IMG1
  targetScale: 0.55
toolbarButton3Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton3ForegroundStyle
  action:
    shortcutCommand: '#RimeSwitcher'
toolbarButton3ForegroundStyle:
  normalImage:
    file: "\u5F00\u5173"
    image: IMG1
  highlightImage:
    file: "\u5F00\u5173"
    image: IMG1
  targetScale: 0.55
toolbarButton4Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton4ForegroundStyle
  action:
    floatKeyboardType: floatsearch
toolbarButton4ForegroundStyle:
  normalImage:
    file: "\u641C\u7D22"
    image: IMG1
  highlightImage:
    file: "\u641C\u7D22"
    image: IMG1
  targetScale: 0.55
toolbarButton5Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton5ForegroundStyle
  action:
    runTranslateScript: "\u667A\u8C31\u7FFB\u8BD1"
toolbarButton5ForegroundStyle:
  normalImage:
    file: "\u7FFB\u8BD1"
    image: IMG1
  highlightImage:
    file: "\u7FFB\u8BD1"
    image: IMG1
  targetScale: 0.55
toolbarButton6Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton6ForegroundStyle
  action:
    openURL: hamster://dev.fuxiao.app.hamster/keyboardSkins
toolbarButton6ForegroundStyle:
  normalImage:
    file: "\u76AE\u80A4"
    image: IMG1
  highlightImage:
    file: "\u76AE\u80A4"
    image: IMG1
  targetScale: 0.55
horizontalCandidateStyle:
  insets:
    left: 5
    top: 5
    bottom: 5
  candidateStateButtonStyle: candidateStateButtonStyle
  highlightBackgroundColor: 00000000
  preferredBackgroundColor: 00000000
  preferredIndexColor: 377DFA
  preferredTextColor: 377DFA
  preferredCommentColor: 377DFA
  indexColor: 070707
  textColor: 070707
  commentColor: 070707
  indexFontSize: 1.19em
  textFontSize: 1.19em
  commentFontSize: 0.81em
  itemSpacing: 5
candidateStateButtonStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle: candidateStateButtonForegroundStyle
candidateStateButtonForegroundStyle:
  systemImageName: "chevron.down"
  fontSize: 1em
  normalColor: 070707
  highlightColor: 070707
verticalCandidateStyle:
  insets:
    top: 3
    bottom: 3
    left: 4
    right: 4
  bottomRowHeight: 43
  backgroundStyle: verticalCandidateBackgroundStyle
  candidateStyle: verticalCandidateOfCandidateStyle
  pageUpButtonStyle: verticalCandidatePageUpButtonStyle
  pageDownButtonStyle: verticalCandidatePageDownButtonStyle
  returnButtonStyle: verticalCandidateReturnButtonStyle
  backspaceButtonStyle: verticalCandidateBackspaceButtonStyle
verticalCandidateOfCandidateStyle:
  insets:
    top: 3
    bottom: 6
    left: 8
    right: 8
  cornerRadius: 9
  backgroundColor: E3E4E9
  separatorColor: 00000000
  highlightBackgroundColor: 00000000
  preferredBackgroundColor: 00000000
  preferredIndexColor: 070707
  preferredTextColor: 070707
  preferredCommentColor: 070707
  indexColor: 070707
  textColor: 070707
  commentColor: 070707
  indexFontSize: 1.125em
  textFontSize: 1.125em
  commentFontSize: 0.75em
verticalCandidatePageUpButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidatePageUpButtonForegroundStyle
verticalCandidateButtonBackgroundStyle:
  insets:
    top: 3
    left: 8
    bottom: 3
    right: 8
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
verticalCandidatePageUpButtonForegroundStyle:
  text: "\uE991"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
verticalCandidatePageDownButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidatePageDownButtonForegroundStyle
verticalCandidatePageDownButtonForegroundStyle:
  text: "\uE992"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
verticalCandidateReturnButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidateReturnButtonForegroundStyle
verticalCandidateReturnButtonForegroundStyle:
  text: "\uE9B8"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
verticalCandidateBackspaceButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle:
  - verticalCandidateBackspaceButtonForegroundStyle
verticalCandidateBackspaceButtonForegroundStyle:
  text: "\uE9AA"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
keyboard:
  style: keyboardStyle
  subviews:
  - VStack:
      style: columnStyle1
      subviews:
      - HStack:
          subviews:
          - Cell: qButton
          - Cell: wButton
          - Cell: eButton
          - Cell: rButton
          - Cell: tButton
      - HStack:
          subviews:
          - Cell: aButton
          - Cell: sButton
          - Cell: dButton
          - Cell: fButton
          - Cell: gButton
      - HStack:
          subviews:
          - Cell: shiftButton
          - Cell: zButton
          - Cell: xButton
          - Cell: cButton
          - Cell: vButton
      - HStack:
          subviews:
          - Cell: symButton
          - Cell: dotButton
          - Cell: spaceButton
  - VStack:
      style: columnStyle2
      subviews:
      - VStack:
          style: VStackStyle1
          subviews:
          - Cell: collection
          - Cell: equalButton
      - VStack:
          style: VStackStyle2
          subviews:
          - Cell: 1Button
          - Cell: 4Button
          - Cell: 7Button
          - Cell: atButton
      - VStack:
          style: VStackStyle2
          subviews:
          - Cell: 2Button
          - Cell: 5Button
          - Cell: 8Button
          - Cell: 0Button
      - VStack:
          style: VStackStyle2
          subviews:
          - Cell: 3Button
          - Cell: 6Button
          - Cell: 9Button
          - Cell: periodButton
  - VStack:
      style: columnStyle3
      subviews:
      - HStack:
          subviews:
          - Cell: yButton
          - Cell: uButton
          - Cell: iButton
          - Cell: oButton
          - Cell: pButton
      - HStack:
          subviews:
          - Cell: gButton
          - Cell: hButton
          - Cell: jButton
          - Cell: kButton
          - Cell: lButton
      - HStack:
          subviews:
          - Cell: vButton
          - Cell: bButton
          - Cell: nButton
          - Cell: mButton
          - Cell: backspaceButton
      - HStack:
          subviews:
          - Cell: spaceButton
          - Cell: cnenButton
          - Cell: enterButton
columnStyle1:
  size:
    width: 5/14
columnStyle2:
  size:
    width: 4/14
columnStyle3:
  size:
    width: 5/14
VStackStyle1:
  size:
    width: 1/4
VStackStyle2:
  size:
    width: 1/4
keyboardStyle:
  insets:
    top: 1
  backgroundStyle: keyboardBackgroundStyle
qButton:
  size:
    width: 146/784
  backgroundStyle: qButtonBackgroundStyle
  foregroundStyle:
  - qButtonForegroundStyle
  - qButtonForegroundStyle1
  - qButtonUpForegroundStyle
  - qButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - qButtonUppercasedStateForegroundStyle
  - qButtonUppercasedStateForegroundStyle1
  - qButtonUpForegroundStyle
  - qButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - qButtonUppercasedStateForegroundStyle
  - qButtonUppercasedStateForegroundStyle1
  - qButtonUpForegroundStyle
  - qButtonDownForegroundStyle
  hintStyle: qButtonHintStyle
  holdSymbolsStyle: qButtonHoldSymbolsStyle
  action:
    character: q
  uppercasedStateAction:
    character: Q
  swipeUpAction:
    character: 1
  swipeDownAction:
    character: '['
qButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
qButtonForegroundStyle1:
  animation: animation
  text: Q
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
qButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: Q
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
qButtonUpForegroundStyle:
  animation: animation
  text: 1
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
qButtonDownForegroundStyle:
  animation: animation
  text: '['
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
qButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: qButtonHintForegroundStyle
  swipeUpForegroundStyle: qButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: qButtonSwipeDownHintForegroundStyle
qButtonHintForegroundStyle:
  text: Q
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
qButtonSwipeUpHintForegroundStyle:
  text: 1
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
qButtonSwipeDownHintForegroundStyle:
  text: '['
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
qButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - qButtonHoldSymbolsOfForegroundStyle0
  - qButtonHoldSymbolsOfForegroundStyle1
  - qButtonHoldSymbolsOfForegroundStyle2
  actions:
  - symbol: "\u58F9"
  - symbol: Q
  - symbol: q
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
qButtonHoldSymbolsOfForegroundStyle0:
  text: "\u58F9"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
qButtonHoldSymbolsOfForegroundStyle1:
  text: Q
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
qButtonHoldSymbolsOfForegroundStyle2:
  text: q
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
wButton:
  size:
    width: 146/784
  backgroundStyle: wButtonBackgroundStyle
  foregroundStyle:
  - wButtonForegroundStyle
  - wButtonForegroundStyle1
  - wButtonUpForegroundStyle
  - wButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - wButtonUppercasedStateForegroundStyle
  - wButtonUppercasedStateForegroundStyle1
  - wButtonUpForegroundStyle
  - wButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - wButtonUppercasedStateForegroundStyle
  - wButtonUppercasedStateForegroundStyle1
  - wButtonUpForegroundStyle
  - wButtonDownForegroundStyle
  hintStyle: wButtonHintStyle
  action:
    character: w
  uppercasedStateAction:
    character: W
  swipeUpAction:
    character: 2
  swipeDownAction:
    character: ']'
  holdSymbolsStyle: wButtonHoldSymbolsStyle
wButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
wButtonForegroundStyle1:
  animation: animation
  text: W
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
wButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: W
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
wButtonUpForegroundStyle:
  animation: animation
  text: 2
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
wButtonDownForegroundStyle:
  animation: animation
  text: ']'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
wButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: wButtonHintForegroundStyle
  swipeUpForegroundStyle: wButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: wButtonSwipeDownHintForegroundStyle
wButtonHintForegroundStyle:
  text: W
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
wButtonSwipeUpHintForegroundStyle:
  text: 2
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
wButtonSwipeDownHintForegroundStyle:
  text: ']'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
wButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - wButtonHoldSymbolsOfForegroundStyle0
  - wButtonHoldSymbolsOfForegroundStyle1
  - wButtonHoldSymbolsOfForegroundStyle2
  actions:
  - symbol: w
  - symbol: "\u8D30"
  - symbol: W
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
wButtonHoldSymbolsOfForegroundStyle0:
  text: w
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
wButtonHoldSymbolsOfForegroundStyle1:
  text: "\u8D30"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
wButtonHoldSymbolsOfForegroundStyle2:
  text: W
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
eButton:
  size:
    width: 146/784
  backgroundStyle: eButtonBackgroundStyle
  foregroundStyle:
  - eButtonForegroundStyle
  - eButtonForegroundStyle1
  - eButtonUpForegroundStyle
  - eButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - eButtonUppercasedStateForegroundStyle
  - eButtonUppercasedStateForegroundStyle1
  - eButtonUpForegroundStyle
  - eButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - eButtonUppercasedStateForegroundStyle
  - eButtonUppercasedStateForegroundStyle1
  - eButtonUpForegroundStyle
  - eButtonDownForegroundStyle
  hintStyle: eButtonHintStyle
  action:
    character: e
  uppercasedStateAction:
    character: E
  swipeUpAction:
    character: 3
  swipeDownAction:
    character: '{'
  holdSymbolsStyle: eButtonHoldSymbolsStyle
eButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
eButtonForegroundStyle1:
  animation: animation
  text: E
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
eButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: E
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
eButtonUpForegroundStyle:
  animation: animation
  text: 3
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
eButtonDownForegroundStyle:
  animation: animation
  text: '{'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
eButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: eButtonHintForegroundStyle
  swipeUpForegroundStyle: eButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: eButtonSwipeDownHintForegroundStyle
eButtonHintForegroundStyle:
  text: E
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
eButtonSwipeUpHintForegroundStyle:
  text: 3
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
eButtonSwipeDownHintForegroundStyle:
  text: '{'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
eButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - eButtonHoldSymbolsForegroundStyle0
  - eButtonHoldSymbolsForegroundStyle1
  - eButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: e
  - symbol: "\u53C1"
  - symbol: E
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
eButtonHoldSymbolsForegroundStyle0:
  text: e
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
eButtonHoldSymbolsForegroundStyle1:
  text: "\u53C1"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
eButtonHoldSymbolsForegroundStyle2:
  text: E
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
rButton:
  size:
    width: 146/784
  backgroundStyle: rButtonBackgroundStyle
  foregroundStyle:
  - rButtonForegroundStyle
  - rButtonForegroundStyle1
  - rButtonUpForegroundStyle
  - rButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - rButtonUppercasedStateForegroundStyle
  - rButtonUppercasedStateForegroundStyle1
  - rButtonUpForegroundStyle
  - rButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - rButtonUppercasedStateForegroundStyle
  - rButtonUppercasedStateForegroundStyle1
  - rButtonUpForegroundStyle
  - rButtonDownForegroundStyle
  hintStyle: rButtonHintStyle
  action:
    character: r
  uppercasedStateAction:
    character: R
  swipeUpAction:
    character: 4
  swipeDownAction:
    character: '}'
  holdSymbolsStyle: rButtonHoldSymbolsStyle
rButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
rButtonForegroundStyle1:
  animation: animation
  text: R
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
rButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: R
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
rButtonUpForegroundStyle:
  animation: animation
  text: 4
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
rButtonDownForegroundStyle:
  animation: animation
  text: '}'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
rButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: rButtonHintForegroundStyle
  swipeUpForegroundStyle: rButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: rButtonSwipeDownHintForegroundStyle
rButtonHintForegroundStyle:
  text: R
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
rButtonSwipeUpHintForegroundStyle:
  text: 6
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
rButtonSwipeDownHintForegroundStyle:
  text: '}'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
rButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - rButtonHoldSymbolsForegroundStyle0
  - rButtonHoldSymbolsForegroundStyle1
  - rButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: r
  - symbol: "\u8086"
  - symbol: R
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
rButtonHoldSymbolsForegroundStyle0:
  text: r
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
rButtonHoldSymbolsForegroundStyle1:
  text: "\u8086"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
rButtonHoldSymbolsForegroundStyle2:
  text: R
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
tButton:
  size:
    width: 200/784
  bounds:
    width: 146/200
    alignment: left
  backgroundStyle: tButtonBackgroundStyle
  foregroundStyle:
  - tButtonForegroundStyle
  - tButtonForegroundStyle1
  - tButtonUpForegroundStyle
  - tButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - tButtonUppercasedStateForegroundStyle
  - tButtonUppercasedStateForegroundStyle1
  - tButtonUpForegroundStyle
  - tButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - tButtonUppercasedStateForegroundStyle
  - tButtonUppercasedStateForegroundStyle1
  - tButtonUpForegroundStyle
  - tButtonDownForegroundStyle
  hintStyle: tButtonHintStyle
  action:
    character: t
  uppercasedStateAction:
    character: T
  swipeUpAction:
    character: 5
  swipeDownAction:
    character: '%'
  holdSymbolsStyle: tButtonHoldSymbolsStyle
tButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
tButtonForegroundStyle1:
  animation: animation
  text: T
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
tButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: T
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
tButtonUpForegroundStyle:
  animation: animation
  text: 5
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
tButtonDownForegroundStyle:
  animation: animation
  text: '%'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
tButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: tButtonHintForegroundStyle
  swipeUpForegroundStyle: tButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: tButtonSwipeDownHintForegroundStyle
tButtonHintForegroundStyle:
  text: T
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
tButtonSwipeUpHintForegroundStyle:
  text: 5
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
tButtonSwipeDownHintForegroundStyle:
  text: '%'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
tButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - tButtonHoldSymbolsForegroundStyle0
  - tButtonHoldSymbolsForegroundStyle1
  - tButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: t
  - symbol: "\u4F0D"
  - symbol: T
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
tButtonHoldSymbolsForegroundStyle0:
  text: t
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
tButtonHoldSymbolsForegroundStyle1:
  text: "\u4F0D"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
tButtonHoldSymbolsForegroundStyle2:
  text: T
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
yButton:
  size:
    width: 200/784
  bounds:
    width: 146/200
    alignment: right
  backgroundStyle: yButtonBackgroundStyle
  foregroundStyle:
  - yButtonForegroundStyle
  - yButtonForegroundStyle1
  - yButtonUpForegroundStyle
  - yButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - yButtonUppercasedStateForegroundStyle
  - yButtonUppercasedStateForegroundStyle1
  - yButtonUpForegroundStyle
  - yButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - yButtonUppercasedStateForegroundStyle
  - yButtonUppercasedStateForegroundStyle1
  - yButtonUpForegroundStyle
  - yButtonDownForegroundStyle
  hintStyle: yButtonHintStyle
  action:
    character: y
  uppercasedStateAction:
    character: Y
  swipeUpAction:
    character: 6
  swipeDownAction:
    character: ^
  holdSymbolsStyle: yButtonHoldSymbolsStyle
yButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
yButtonForegroundStyle1:
  animation: animation
  text: Y
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
yButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: Y
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
yButtonUpForegroundStyle:
  animation: animation
  text: 6
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
yButtonDownForegroundStyle:
  animation: animation
  text: ^
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
yButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: yButtonHintForegroundStyle
  swipeUpForegroundStyle: yButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: yButtonSwipeDownHintForegroundStyle
yButtonHintForegroundStyle:
  text: Y
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
yButtonSwipeUpHintForegroundStyle:
  text: 6
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
yButtonSwipeDownHintForegroundStyle:
  text: '^'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
yButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - yButtonHoldSymbolsForegroundStyle0
  - yButtonHoldSymbolsForegroundStyle1
  - yButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: y
  - symbol: "\u9646"
  - symbol: Y
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
yButtonHoldSymbolsForegroundStyle0:
  text: y
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
yButtonHoldSymbolsForegroundStyle1:
  text: "\u9646"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
yButtonHoldSymbolsForegroundStyle2:
  text: Y
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
uButton:
  size:
    width: 146/784
  backgroundStyle: uButtonBackgroundStyle
  foregroundStyle:
  - uButtonForegroundStyle
  - uButtonForegroundStyle1
  - uButtonUpForegroundStyle
  - uButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - uButtonUppercasedStateForegroundStyle
  - uButtonUppercasedStateForegroundStyle1
  - uButtonUpForegroundStyle
  - uButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - uButtonUppercasedStateForegroundStyle
  - uButtonUppercasedStateForegroundStyle1
  - uButtonUpForegroundStyle
  - uButtonDownForegroundStyle
  hintStyle: uButtonHintStyle
  action:
    character: u
  uppercasedStateAction:
    character: U
  swipeUpAction:
    character: 7
  swipeDownAction:
    character: '&'
  holdSymbolsStyle: uButtonHoldSymbolsStyle
uButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
uButtonForegroundStyle1:
  animation: animation
  text: U
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
uButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: U
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
uButtonUpForegroundStyle:
  animation: animation
  text: 7
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
uButtonDownForegroundStyle:
  animation: animation
  text: '&'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
uButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: uButtonHintForegroundStyle
  swipeUpForegroundStyle: uButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: uButtonSwipeDownHintForegroundStyle
uButtonHintForegroundStyle:
  text: U
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
uButtonSwipeUpHintForegroundStyle:
  text: 7
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
uButtonSwipeDownHintForegroundStyle:
  text: '&'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
uButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - uButtonHoldSymbolsForegroundStyle0
  - uButtonHoldSymbolsForegroundStyle1
  - uButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: u
  - symbol: "\u67D2"
  - symbol: U
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
uButtonHoldSymbolsForegroundStyle0:
  text: u
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
uButtonHoldSymbolsForegroundStyle1:
  text: "\u67D2"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
uButtonHoldSymbolsForegroundStyle2:
  text: U
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
iButton:
  size:
    width: 146/784
  backgroundStyle: iButtonBackgroundStyle
  foregroundStyle:
  - iButtonForegroundStyle
  - iButtonForegroundStyle1
  - iButtonUpForegroundStyle
  - iButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - iButtonUppercasedStateForegroundStyle
  - iButtonUppercasedStateForegroundStyle1
  - iButtonUpForegroundStyle
  - iButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - iButtonUppercasedStateForegroundStyle
  - iButtonUppercasedStateForegroundStyle1
  - iButtonUpForegroundStyle
  - iButtonDownForegroundStyle
  hintStyle: iButtonHintStyle
  action:
    character: i
  uppercasedStateAction:
    character: I
  swipeUpAction:
    character: 8
  swipeDownAction:
    character: '*'
  holdSymbolsStyle: iButtonHoldSymbolsStyle
iButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
iButtonForegroundStyle1:
  animation: animation
  text: I
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
iButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: I
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
iButtonUpForegroundStyle:
  animation: animation
  text: 8
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
iButtonDownForegroundStyle:
  animation: animation
  text: '*'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
iButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: iButtonHintForegroundStyle
  swipeUpForegroundStyle: iButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: iButtonSwipeDownHintForegroundStyle
iButtonHintForegroundStyle:
  text: I
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
iButtonSwipeUpHintForegroundStyle:
  text: 8
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
iButtonSwipeDownHintForegroundStyle:
  text: '*'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
iButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - iButtonHoldSymbolsForegroundStyle0
  - iButtonHoldSymbolsForegroundStyle1
  - iButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: i
  - symbol: "\u634C"
  - symbol: I
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
iButtonHoldSymbolsForegroundStyle0:
  text: i
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
iButtonHoldSymbolsForegroundStyle1:
  text: "\u634C"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
iButtonHoldSymbolsForegroundStyle2:
  text: I
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
oButton:
  size:
    width: 146/784
  backgroundStyle: oButtonBackgroundStyle
  foregroundStyle:
  - oButtonForegroundStyle
  - oButtonForegroundStyle1
  - oButtonUpForegroundStyle
  - oButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - oButtonUppercasedStateForegroundStyle
  - oButtonUppercasedStateForegroundStyle1
  - oButtonUpForegroundStyle
  - oButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - oButtonUppercasedStateForegroundStyle
  - oButtonUppercasedStateForegroundStyle1
  - oButtonUpForegroundStyle
  - oButtonDownForegroundStyle
  hintStyle: oButtonHintStyle
  action:
    character: o
  uppercasedStateAction:
    character: O
  swipeUpAction:
    character: 9
  swipeDownAction:
    character: +
  holdSymbolsStyle: oButtonHoldSymbolsStyle
oButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
oButtonForegroundStyle1:
  animation: animation
  text: O
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
oButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: O
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
oButtonUpForegroundStyle:
  animation: animation
  text: 9
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
oButtonDownForegroundStyle:
  animation: animation
  text: +
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
oButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: oButtonHintForegroundStyle
  swipeUpForegroundStyle: oButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: oButtonSwipeDownHintForegroundStyle
oButtonHintForegroundStyle:
  text: O
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
oButtonSwipeUpHintForegroundStyle:
  text: 9
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
oButtonSwipeDownHintForegroundStyle:
  text: '+'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
oButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - oButtonHoldSymbolsForegroundStyle0
  - oButtonHoldSymbolsForegroundStyle1
  - oButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: o
  - symbol: "\u7396"
  - symbol: O
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
oButtonHoldSymbolsForegroundStyle0:
  text: o
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
oButtonHoldSymbolsForegroundStyle1:
  text: "\u7396"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
oButtonHoldSymbolsForegroundStyle2:
  text: O
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
pButton:
  size:
    width: 146/784
  backgroundStyle: pButtonBackgroundStyle
  foregroundStyle:
  - pButtonForegroundStyle
  - pButtonForegroundStyle1
  - pButtonUpForegroundStyle
  - pButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - pButtonUppercasedStateForegroundStyle
  - pButtonUppercasedStateForegroundStyle1
  - pButtonUpForegroundStyle
  - pButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - pButtonUppercasedStateForegroundStyle
  - pButtonUppercasedStateForegroundStyle1
  - pButtonUpForegroundStyle
  - pButtonDownForegroundStyle
  hintStyle: pButtonHintStyle
  action:
    character: p
  uppercasedStateAction:
    character: P
  swipeUpAction:
    character: 0
  swipeDownAction:
    character: '='
  holdSymbolsStyle: pButtonHoldSymbolsStyle
pButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
pButtonForegroundStyle1:
  animation: animation
  text: P
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
pButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: P
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
pButtonUpForegroundStyle:
  animation: animation
  text: 0
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
pButtonDownForegroundStyle:
  animation: animation
  text: '='
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
pButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: pButtonHintForegroundStyle
  swipeUpForegroundStyle: pButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: pButtonSwipeDownHintForegroundStyle
pButtonHintForegroundStyle:
  text: P
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
pButtonSwipeUpHintForegroundStyle:
  text: 0
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
pButtonSwipeDownHintForegroundStyle:
  text: '='
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
pButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - pButtonHoldSymbolsOfForegroundStyle0
  - pButtonHoldSymbolsOfForegroundStyle1
  - pButtonHoldSymbolsOfForegroundStyle2
  actions:
  - symbol: p
  - symbol: P
  - symbol: "\u96F6"
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 2
pButtonHoldSymbolsOfForegroundStyle0:
  text: p
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
pButtonHoldSymbolsOfForegroundStyle1:
  text: P
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
pButtonHoldSymbolsOfForegroundStyle2:
  text: "\u96F6"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
aButton:
  size:
    width: 200/784
  bounds:
    width: 146/200
    alignment: right
  backgroundStyle: aButtonBackgroundStyle
  foregroundStyle:
  - aButtonForegroundStyle
  - aButtonForegroundStyle1
  - aButtonUpForegroundStyle
  - aButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - aButtonUppercasedStateForegroundStyle
  - aButtonUppercasedStateForegroundStyle1
  - aButtonUpForegroundStyle
  - aButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - aButtonUppercasedStateForegroundStyle
  - aButtonUppercasedStateForegroundStyle1
  - aButtonUpForegroundStyle
  - aButtonDownForegroundStyle
  hintStyle: aButtonHintStyle
  action:
    character: a
  uppercasedStateAction:
    character: A
  swipeUpAction:
    character: '~'
  swipeDownAction:
    character: '`'
  holdSymbolsStyle: aButtonHoldSymbolsStyle
aButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
aButtonForegroundStyle1:
  animation: animation
  text: A
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
aButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: A
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
aButtonUpForegroundStyle:
  animation: animation
  text: '~'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
aButtonDownForegroundStyle:
  animation: animation
  text: '`'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
aButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: aButtonHintForegroundStyle
  swipeUpForegroundStyle: aButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: aButtonSwipeDownHintForegroundStyle
aButtonHintForegroundStyle:
  text: A
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
aButtonSwipeUpHintForegroundStyle:
  text: '~'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
aButtonSwipeDownHintForegroundStyle:
  text: '`'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
aButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - aButtonHoldSymbolsForegroundStyle0
  - aButtonHoldSymbolsForegroundStyle1
  actions:
  - symbol: '~'
  - symbol: "\u2026\u2026"
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
aButtonHoldSymbolsForegroundStyle0:
  text: '~'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
aButtonHoldSymbolsForegroundStyle1:
  text: "\u2026\u2026"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
sButton:
  size:
    width: 146/784
  backgroundStyle: sButtonBackgroundStyle
  foregroundStyle:
  - sButtonForegroundStyle
  - sButtonForegroundStyle1
  - sButtonUpForegroundStyle
  - sButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - sButtonUppercasedStateForegroundStyle
  - sButtonUppercasedStateForegroundStyle1
  - sButtonUpForegroundStyle
  - sButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - sButtonUppercasedStateForegroundStyle
  - sButtonUppercasedStateForegroundStyle1
  - sButtonUpForegroundStyle
  - sButtonDownForegroundStyle
  hintStyle: sButtonHintStyle
  action:
    character: s
  uppercasedStateAction:
    character: S
  swipeUpAction:
    character: /
  swipeDownAction:
    symbol: \
  holdSymbolsStyle: sButtonHoldSymbolsStyle
sButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
sButtonForegroundStyle1:
  animation: animation
  text: S
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
sButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: S
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
sButtonUpForegroundStyle:
  animation: animation
  text: /
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
sButtonDownForegroundStyle:
  animation: animation
  text: \
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
sButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: sButtonHintForegroundStyle
  swipeUpForegroundStyle: sButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: sButtonSwipeDownHintForegroundStyle
sButtonHintForegroundStyle:
  text: S
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
sButtonSwipeUpHintForegroundStyle:
  text: /
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
sButtonSwipeDownHintForegroundStyle:
  text: '\'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
sButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - sButtonHoldSymbolsForegroundStyle0
  - sButtonHoldSymbolsForegroundStyle1
  - sButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: \n
  - symbol: /
  - symbol: '[\s\S]'
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
sButtonHoldSymbolsForegroundStyle0:
  text: \n
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
sButtonHoldSymbolsForegroundStyle1:
  text: /
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
sButtonHoldSymbolsForegroundStyle2:
  text: '[\s\S]'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
dButton:
  size:
    width: 146/784
  backgroundStyle: dButtonBackgroundStyle
  foregroundStyle:
  - dButtonForegroundStyle
  - dButtonForegroundStyle1
  - dButtonUpForegroundStyle
  - dButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - dButtonUppercasedStateForegroundStyle
  - dButtonUppercasedStateForegroundStyle1
  - dButtonUpForegroundStyle
  - dButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - dButtonUppercasedStateForegroundStyle
  - dButtonUppercasedStateForegroundStyle1
  - dButtonUpForegroundStyle
  - dButtonDownForegroundStyle
  hintStyle: dButtonHintStyle
  action:
    character: d
  uppercasedStateAction:
    character: D
  swipeUpAction:
    character: ':'
  swipeDownAction:
    character: "\u3001"
  holdSymbolsStyle: dButtonHoldSymbolsStyle
dButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
dButtonForegroundStyle1:
  animation: animation
  text: D
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
dButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: D
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
dButtonUpForegroundStyle:
  animation: animation
  text: ':'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
dButtonDownForegroundStyle:
  animation: animation
  text: "\u3001"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
dButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: dButtonHintForegroundStyle
  swipeUpForegroundStyle: dButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: dButtonSwipeDownHintForegroundStyle
dButtonHintForegroundStyle:
  text: D
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
dButtonSwipeUpHintForegroundStyle:
  text: ':'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
dButtonSwipeDownHintForegroundStyle:
  text: "\u3001"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
dButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - dButtonHoldSymbolsOfForegroundStyle0
  - dButtonHoldSymbolsOfForegroundStyle1
  - dButtonHoldSymbolsOfForegroundStyle2
  actions:
  - sendKeys: /time
  - symbol: ':'
  - sendKeys: /date
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
dButtonHoldSymbolsOfForegroundStyle0:
  text: "\u23F0"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
dButtonHoldSymbolsOfForegroundStyle1:
  text: ':'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
dButtonHoldSymbolsOfForegroundStyle2:
  text: "\U0001F5D3"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
fButton:
  size:
    width: 146/784
  backgroundStyle: fButtonBackgroundStyle
  foregroundStyle:
  - fButtonForegroundStyle
  - fButtonForegroundStyle1
  - fButtonUpForegroundStyle
  - fButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - fButtonUppercasedStateForegroundStyle
  - fButtonUppercasedStateForegroundStyle1
  - fButtonUpForegroundStyle
  - fButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - fButtonUppercasedStateForegroundStyle
  - fButtonUppercasedStateForegroundStyle1
  - fButtonUpForegroundStyle
  - fButtonDownForegroundStyle
  hintStyle: fButtonHintStyle
  action:
    character: f
  uppercasedStateAction:
    character: F
  swipeUpAction:
    character: ;
  swipeDownAction:
    character: '|'
  holdSymbolsStyle: fButtonHoldSymbolsStyle
fButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
fButtonForegroundStyle1:
  animation: animation
  text: F
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
fButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: F
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
fButtonUpForegroundStyle:
  animation: animation
  text: ;
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
fButtonDownForegroundStyle:
  animation: animation
  text: '|'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
fButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: fButtonHintForegroundStyle
  swipeUpForegroundStyle: fButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: fButtonSwipeDownHintForegroundStyle
fButtonHintForegroundStyle:
  text: F
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
fButtonSwipeUpHintForegroundStyle:
  text: ;
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
fButtonSwipeDownHintForegroundStyle:
  text: '|'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
fButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - fButtonHoldSymbolsForegroundStyle0
  actions:
  - symbol: ;
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
fButtonHoldSymbolsForegroundStyle0:
  text: ;
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
gButton:
  size:
    width: 146/784
  backgroundStyle: gButtonBackgroundStyle
  foregroundStyle:
  - gButtonForegroundStyle
  - gButtonForegroundStyle1
  - gButtonUpForegroundStyle
  - gButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - gButtonUppercasedStateForegroundStyle
  - gButtonUppercasedStateForegroundStyle1
  - gButtonUpForegroundStyle
  - gButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - gButtonUppercasedStateForegroundStyle
  - gButtonUppercasedStateForegroundStyle1
  - gButtonUpForegroundStyle
  - gButtonDownForegroundStyle
  hintStyle: gButtonHintStyle
  action:
    character: g
  uppercasedStateAction:
    character: G
  swipeUpAction:
    character: (
  swipeDownAction:
    character: <
  holdSymbolsStyle: gButtonHoldSymbolsStyle
gButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
gButtonForegroundStyle1:
  animation: animation
  text: G
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
gButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: G
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
gButtonUpForegroundStyle:
  animation: animation
  text: (
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
gButtonDownForegroundStyle:
  animation: animation
  text: <
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
gButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: gButtonHintForegroundStyle
  swipeUpForegroundStyle: gButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: gButtonSwipeDownHintForegroundStyle
gButtonHintForegroundStyle:
  text: G
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
gButtonSwipeUpHintForegroundStyle:
  text: (
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
gButtonSwipeDownHintForegroundStyle:
  text: '<'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
gButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - gButtonHoldSymbolsForegroundStyle0
  - gButtonHoldSymbolsForegroundStyle1
  - gButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: <
  - symbol: (
  - symbol: '['
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
gButtonHoldSymbolsForegroundStyle0:
  text: <
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
gButtonHoldSymbolsForegroundStyle1:
  text: (
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
gButtonHoldSymbolsForegroundStyle2:
  text: '['
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
hButton:
  size:
    width: 146/784
  backgroundStyle: hButtonBackgroundStyle
  foregroundStyle:
  - hButtonForegroundStyle
  - hButtonForegroundStyle1
  - hButtonUpForegroundStyle
  - hButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - hButtonUppercasedStateForegroundStyle
  - hButtonUppercasedStateForegroundStyle1
  - hButtonUpForegroundStyle
  - hButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - hButtonUppercasedStateForegroundStyle
  - hButtonUppercasedStateForegroundStyle1
  - hButtonUpForegroundStyle
  - hButtonDownForegroundStyle
  hintStyle: hButtonHintStyle
  action:
    character: h
  uppercasedStateAction:
    character: H
  swipeUpAction:
    character: )
  swipeDownAction:
    character: '>'
  holdSymbolsStyle: hButtonHoldSymbolsStyle
hButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
hButtonForegroundStyle1:
  animation: animation
  text: H
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
hButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: H
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
hButtonUpForegroundStyle:
  animation: animation
  text: )
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
hButtonDownForegroundStyle:
  animation: animation
  text: '>'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
hButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: hButtonHintForegroundStyle
  swipeUpForegroundStyle: hButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: hButtonSwipeDownHintForegroundStyle
hButtonHintForegroundStyle:
  text: H
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
hButtonSwipeUpHintForegroundStyle:
  text: )
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
hButtonSwipeDownHintForegroundStyle:
  text: '>'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
hButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - hButtonHoldSymbolsForegroundStyle0
  - hButtonHoldSymbolsForegroundStyle1
  - hButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: '>'
  - symbol: )
  - symbol: ']'
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
hButtonHoldSymbolsForegroundStyle0:
  text: '>'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
hButtonHoldSymbolsForegroundStyle1:
  text: )
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
hButtonHoldSymbolsForegroundStyle2:
  text: ']'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
jButton:
  size:
    width: 146/784
  backgroundStyle: jButtonBackgroundStyle
  foregroundStyle:
  - jButtonForegroundStyle
  - jButtonForegroundStyle1
  - jButtonUpForegroundStyle
  - jButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - jButtonUppercasedStateForegroundStyle
  - jButtonUppercasedStateForegroundStyle1
  - jButtonUpForegroundStyle
  - jButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - jButtonUppercasedStateForegroundStyle
  - jButtonUppercasedStateForegroundStyle1
  - jButtonUpForegroundStyle
  - jButtonDownForegroundStyle
  hintStyle: jButtonHintStyle
  action:
    character: j
  uppercasedStateAction:
    character: J
  swipeUpAction:
    character: '@'
  swipeDownAction:
    character: $
  holdSymbolsStyle: jButtonHoldSymbolsStyle
jButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
jButtonForegroundStyle1:
  animation: animation
  text: J
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
jButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: J
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
jButtonUpForegroundStyle:
  animation: animation
  text: '@'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
jButtonDownForegroundStyle:
  animation: animation
  text: $
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
jButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: jButtonHintForegroundStyle
  swipeUpForegroundStyle: jButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: jButtonSwipeDownHintForegroundStyle
jButtonHintForegroundStyle:
  text: J
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
jButtonSwipeUpHintForegroundStyle:
  text: '@'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
jButtonSwipeDownHintForegroundStyle:
  text: '$'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
jButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - jButtonHoldSymbolsForegroundStyle0
  - jButtonHoldSymbolsForegroundStyle1
  - jButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: $
  - symbol: '@'
  - symbol: "\u20AC"
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
jButtonHoldSymbolsForegroundStyle0:
  text: $
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
jButtonHoldSymbolsForegroundStyle1:
  text: '@'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
jButtonHoldSymbolsForegroundStyle2:
  text: "\u20AC"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
kButton:
  size:
    width: 146/784
  backgroundStyle: kButtonBackgroundStyle
  foregroundStyle:
  - kButtonForegroundStyle
  - kButtonForegroundStyle1
  - kButtonUpForegroundStyle
  - kButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - kButtonUppercasedStateForegroundStyle
  - kButtonUppercasedStateForegroundStyle1
  - kButtonUpForegroundStyle
  - kButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - kButtonUppercasedStateForegroundStyle
  - kButtonUppercasedStateForegroundStyle1
  - kButtonUpForegroundStyle
  - kButtonDownForegroundStyle
  hintStyle: kButtonHintStyle
  action:
    character: k
  uppercasedStateAction:
    character: K
  swipeUpAction:
    character: '"'
  swipeDownAction:
    symbol: '"'
  holdSymbolsStyle: kButtonHoldSymbolsStyle
kButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
kButtonForegroundStyle1:
  animation: animation
  text: K
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
kButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: K
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
kButtonUpForegroundStyle:
  animation: animation
  text: '"'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
kButtonDownForegroundStyle:
  animation: animation
  text: '"'
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
kButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: kButtonHintForegroundStyle
  swipeUpForegroundStyle: kButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: kButtonSwipeDownHintForegroundStyle
kButtonHintForegroundStyle:
  text: K
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
kButtonSwipeUpHintForegroundStyle:
  text: '"'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
kButtonSwipeDownHintForegroundStyle:
  text: '"'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
kButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - kButtonHoldSymbolsForegroundStyle0
  - kButtonHoldSymbolsForegroundStyle1
  - kButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: "\u300C"
  - symbol: '"'
  - symbol: "\u300E"
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
kButtonHoldSymbolsForegroundStyle0:
  text: "\u300C"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
kButtonHoldSymbolsForegroundStyle1:
  text: '"'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
kButtonHoldSymbolsForegroundStyle2:
  text: "\u300E"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
lButton:
  size:
    width: 200/784
  bounds:
    width: 146/200
    alignment: left
  backgroundStyle: lButtonBackgroundStyle
  foregroundStyle:
  - lButtonForegroundStyle
  - lButtonForegroundStyle1
  - lButtonUpForegroundStyle
  - lButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - lButtonUppercasedStateForegroundStyle
  - lButtonUppercasedStateForegroundStyle1
  - lButtonUpForegroundStyle
  - lButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - lButtonUppercasedStateForegroundStyle
  - lButtonUppercasedStateForegroundStyle1
  - lButtonUpForegroundStyle
  - lButtonDownForegroundStyle
  hintStyle: lButtonHintStyle
  action:
    character: l
  uppercasedStateAction:
    character: L
  swipeUpAction:
    character: "'"
  swipeDownAction:
    symbol: "'"
  holdSymbolsStyle: lButtonHoldSymbolsStyle
lButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
lButtonForegroundStyle1:
  animation: animation
  text: L
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
lButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: L
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
lButtonUpForegroundStyle:
  animation: animation
  text: "'"
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
lButtonDownForegroundStyle:
  animation: animation
  text: "'"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
lButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: lButtonHintForegroundStyle
  swipeUpForegroundStyle: lButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: lButtonSwipeDownHintForegroundStyle
lButtonHintForegroundStyle:
  text: L
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
lButtonSwipeUpHintForegroundStyle:
  text: "'"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
lButtonSwipeDownHintForegroundStyle:
  text: "'"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
lButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - lButtonHoldSymbolsForegroundStyle0
  - lButtonHoldSymbolsForegroundStyle1
  - lButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: "\u300D"
  - symbol: "\u300F"
  - symbol: "'"
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 2
lButtonHoldSymbolsForegroundStyle0:
  text: "\u300D"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
lButtonHoldSymbolsForegroundStyle1:
  text: "\u300F"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
lButtonHoldSymbolsForegroundStyle2:
  text: "'"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
shiftButton:
  size:
    width: 200/784
  bounds:
    width: 190/200
    alignment: left
  backgroundStyle: shiftButtonBackgroundStyle
  foregroundStyle: shiftButtonForegroundStyle
  uppercasedStateForegroundStyle: shiftButtonUppercasedForegroundStyle
  capsLockedStateForegroundStyle: shiftButtonCapsLockedForegroundStyle
  action: shift
  swipeUpAction: tab
  swipeDownAction:
    shortcutCommand: '#capsLocked'
shiftButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
shiftButtonForegroundStyle:
  animation: animation
  systemImageName: shift
  center:
    x: 0.5
    y: 0.5
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
shiftButtonUppercasedForegroundStyle:
  animation: animation
  systemImageName: shift.fill
  center:
    x: 0.5
    y: 0.5
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
shiftButtonCapsLockedForegroundStyle:
  animation: animation
  systemImageName: capslock.fill
  center:
    x: 0.5
    y: 0.5
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
zButton:
  size:
    width: 146/784
  backgroundStyle: zButtonBackgroundStyle
  foregroundStyle:
  - zButtonForegroundStyle
  - zButtonForegroundStyle1
  - zButtonUpForegroundStyle
  - zButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - zButtonUppercasedStateForegroundStyle
  - zButtonUppercasedStateForegroundStyle1
  - zButtonUpForegroundStyle
  - zButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - zButtonUppercasedStateForegroundStyle
  - zButtonUppercasedStateForegroundStyle1
  - zButtonUpForegroundStyle
  - zButtonDownForegroundStyle
  hintStyle: zButtonHintStyle
  action:
    character: z
  uppercasedStateAction:
    character: Z
  swipeUpAction:
    character: '-'
  swipeDownAction:
    shortcutCommand: '#selectText'
  holdSymbolsStyle: zButtonHoldSymbolsStyle
zButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
zButtonForegroundStyle1:
  animation: animation
  text: Z
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
zButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: Z
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
zButtonUpForegroundStyle:
  animation: animation
  text: '-'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
zButtonDownForegroundStyle:
  animation: animation
  text: "\U000F0C52"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
zButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: zButtonHintForegroundStyle
  swipeUpForegroundStyle: zButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: zButtonSwipeDownHintForegroundStyle
zButtonHintForegroundStyle:
  text: Z
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
zButtonSwipeUpHintForegroundStyle:
  text: '-'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
zButtonSwipeDownHintForegroundStyle:
  text: "\U000F0C52"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
zButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - zButtonHoldSymbolsForegroundStyle0
  - zButtonHoldSymbolsForegroundStyle1
  - zButtonHoldSymbolsForegroundStyle2
  actions:
  - symbol: "\xB4"
  - symbol: '-'
  - symbol: '`'
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
zButtonHoldSymbolsForegroundStyle0:
  text: "\xB4"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
zButtonHoldSymbolsForegroundStyle1:
  text: '-'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
zButtonHoldSymbolsForegroundStyle2:
  text: '`'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
xButton:
  size:
    width: 146/784
  backgroundStyle: xButtonBackgroundStyle
  foregroundStyle:
  - xButtonForegroundStyle
  - xButtonForegroundStyle1
  - xButtonUpForegroundStyle
  - xButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - xButtonUppercasedStateForegroundStyle
  - xButtonUppercasedStateForegroundStyle1
  - xButtonUpForegroundStyle
  - xButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - xButtonUppercasedStateForegroundStyle
  - xButtonUppercasedStateForegroundStyle1
  - xButtonUpForegroundStyle
  - xButtonDownForegroundStyle
  hintStyle: xButtonHintStyle
  action:
    character: x
  uppercasedStateAction:
    character: X
  swipeUpAction:
    character: _
  swipeDownAction:
    shortcutCommand: "#\u526A\u5207"
  holdSymbolsStyle: xButtonHoldSymbolsStyle
xButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
xButtonForegroundStyle1:
  animation: animation
  text: X
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
xButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: X
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
xButtonUpForegroundStyle:
  animation: animation
  text: _
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
xButtonDownForegroundStyle:
  animation: animation
  text: "\uF0C4"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
xButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: xButtonHintForegroundStyle
  swipeUpForegroundStyle: xButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: xButtonSwipeDownHintForegroundStyle
xButtonHintForegroundStyle:
  text: X
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
xButtonSwipeUpHintForegroundStyle:
  text: _
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
xButtonSwipeDownHintForegroundStyle:
  text: "\uF0C4"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
xButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - xButtonHoldSymbolsForegroundStyle0
  - xButtonHoldSymbolsForegroundStyle1
  actions:
  - symbol: _
  - symbol: "\u2014\u2014"
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
xButtonHoldSymbolsForegroundStyle0:
  text: _
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
xButtonHoldSymbolsForegroundStyle1:
  text: "\u2014\u2014"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cButton:
  size:
    width: 146/784
  backgroundStyle: cButtonBackgroundStyle
  foregroundStyle:
  - cButtonForegroundStyle
  - cButtonForegroundStyle1
  - cButtonUpForegroundStyle
  - cButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - cButtonUppercasedStateForegroundStyle
  - cButtonUppercasedStateForegroundStyle1
  - cButtonUpForegroundStyle
  - cButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - cButtonUppercasedStateForegroundStyle
  - cButtonUppercasedStateForegroundStyle1
  - cButtonUpForegroundStyle
  - cButtonDownForegroundStyle
  hintStyle: cButtonHintStyle
  action:
    character: c
  uppercasedStateAction:
    character: C
  swipeUpAction:
    character: '#'
  swipeDownAction:
    shortcutCommand: "#\u590D\u5236"
  holdSymbolsStyle: cButtonHoldSymbolsStyle
cButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
cButtonForegroundStyle1:
  animation: animation
  text: C
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
cButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: C
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
cButtonUpForegroundStyle:
  animation: animation
  text: '#'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
cButtonDownForegroundStyle:
  animation: animation
  text: "\uEBCC"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
cButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: cButtonHintForegroundStyle
  swipeUpForegroundStyle: cButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: cButtonSwipeDownHintForegroundStyle
cButtonHintForegroundStyle:
  text: C
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
cButtonSwipeUpHintForegroundStyle:
  text: '#'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
cButtonSwipeDownHintForegroundStyle:
  text: "\uEBCC"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
cButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - cButtonHoldSymbolsForegroundStyle0
  actions:
  - symbol: '#'
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
cButtonHoldSymbolsForegroundStyle0:
  text: '#'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
vButton:
  size:
    width: 146/784
  backgroundStyle: vButtonBackgroundStyle
  foregroundStyle:
  - vButtonForegroundStyle
  - vButtonForegroundStyle1
  - vButtonUpForegroundStyle
  - vButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - vButtonUppercasedStateForegroundStyle
  - vButtonUppercasedStateForegroundStyle1
  - vButtonUpForegroundStyle
  - vButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - vButtonUppercasedStateForegroundStyle
  - vButtonUppercasedStateForegroundStyle1
  - vButtonUpForegroundStyle
  - vButtonDownForegroundStyle
  hintStyle: vButtonHintStyle
  action:
    character: v
  uppercasedStateAction:
    character: V
  swipeUpAction:
    character: '?'
  swipeDownAction:
    shortcutCommand: "#\u7C98\u8D34"
  holdSymbolsStyle: vButtonHoldSymbolsStyle
vButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
vButtonForegroundStyle1:
  animation: animation
  text: V
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
vButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: V
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
vButtonUpForegroundStyle:
  animation: animation
  text: '?'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
vButtonDownForegroundStyle:
  animation: animation
  text: "\uF429"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
vButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: vButtonHintForegroundStyle
  swipeUpForegroundStyle: vButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: vButtonSwipeDownHintForegroundStyle
vButtonHintForegroundStyle:
  text: V
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
vButtonSwipeUpHintForegroundStyle:
  text: '?'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
vButtonSwipeDownHintForegroundStyle:
  text: "\uF429"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
vButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - vButtonHoldSymbolsForegroundStyle0
  actions:
  - symbol: '?'
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
vButtonHoldSymbolsForegroundStyle0:
  text: '?'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
bButton:
  size:
    width: 146/784
  backgroundStyle: bButtonBackgroundStyle
  foregroundStyle:
  - bButtonForegroundStyle
  - bButtonForegroundStyle1
  - bButtonUpForegroundStyle
  - bButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - bButtonUppercasedStateForegroundStyle
  - bButtonUppercasedStateForegroundStyle1
  - bButtonUpForegroundStyle
  - bButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - bButtonUppercasedStateForegroundStyle
  - bButtonUppercasedStateForegroundStyle1
  - bButtonUpForegroundStyle
  - bButtonDownForegroundStyle
  hintStyle: bButtonHintStyle
  action:
    character: b
  uppercasedStateAction:
    character: B
  swipeUpAction:
    character: '!'
  swipeDownAction:
    floatKeyboardType: floatswitch
  holdSymbolsStyle: bButtonHoldSymbolsStyle
bButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
bButtonForegroundStyle1:
  animation: animation
  text: B
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
bButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: B
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
bButtonUpForegroundStyle:
  animation: animation
  text: '!'
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
bButtonDownForegroundStyle:
  animation: animation
  text: "\uEB52"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
bButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: bButtonHintForegroundStyle
  swipeUpForegroundStyle: bButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: bButtonSwipeDownHintForegroundStyle
bButtonHintForegroundStyle:
  text: B
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
bButtonSwipeUpHintForegroundStyle:
  text: '!'
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
bButtonSwipeDownHintForegroundStyle:
  text: "\uEB52"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
bButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - bButtonHoldSymbolsForegroundStyle0
  actions:
  - symbol: '!'
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
bButtonHoldSymbolsForegroundStyle0:
  text: '!'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
nButton:
  size:
    width: 146/784
  backgroundStyle: nButtonBackgroundStyle
  foregroundStyle:
  - nButtonForegroundStyle
  - nButtonForegroundStyle1
  - nButtonUpForegroundStyle
  - nButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - nButtonUppercasedStateForegroundStyle
  - nButtonUppercasedStateForegroundStyle1
  - nButtonUpForegroundStyle
  - nButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - nButtonUppercasedStateForegroundStyle
  - nButtonUppercasedStateForegroundStyle1
  - nButtonUpForegroundStyle
  - nButtonDownForegroundStyle
  hintStyle: nButtonHintStyle
  action:
    character: n
  uppercasedStateAction:
    character: N
  swipeUpAction:
    character: ','
  swipeDownAction:
    shortcutCommand: "#\u884C\u9996"
  holdSymbolsStyle: nButtonHoldSymbolsStyle
nButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
nButtonForegroundStyle1:
  animation: animation
  text: N
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
nButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: N
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
nButtonUpForegroundStyle:
  animation: animation
  text: ','
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
nButtonDownForegroundStyle:
  animation: animation
  text: "\uF4F1"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
nButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: nButtonHintForegroundStyle
  swipeUpForegroundStyle: nButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: nButtonSwipeDownHintForegroundStyle
nButtonHintForegroundStyle:
  text: N
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
nButtonSwipeUpHintForegroundStyle:
  text: ','
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
nButtonSwipeDownHintForegroundStyle:
  text: "\uF4F1"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
nButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - nButtonHoldSymbolsForegroundStyle0
  - nButtonHoldSymbolsForegroundStyle1
  actions:
  - symbol: ','
  - symbol: "\u3001"
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 0
nButtonHoldSymbolsForegroundStyle0:
  text: ','
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
nButtonHoldSymbolsForegroundStyle1:
  text: "\u3001"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
mButton:
  size:
    width: 146/784
  backgroundStyle: mButtonBackgroundStyle
  foregroundStyle:
  - mButtonForegroundStyle
  - mButtonForegroundStyle1
  - mButtonUpForegroundStyle
  - mButtonDownForegroundStyle
  uppercasedStateForegroundStyle:
  - mButtonUppercasedStateForegroundStyle
  - mButtonUppercasedStateForegroundStyle1
  - mButtonUpForegroundStyle
  - mButtonDownForegroundStyle
  capsLockedStateForegroundStyle:
  - mButtonUppercasedStateForegroundStyle
  - mButtonUppercasedStateForegroundStyle1
  - mButtonUpForegroundStyle
  - mButtonDownForegroundStyle
  hintStyle: mButtonHintStyle
  action:
    character: m
  uppercasedStateAction:
    character: M
  swipeUpAction:
    character: .
  swipeDownAction:
    shortcutCommand: "#\u884C\u5C3E"
  holdSymbolsStyle: mButtonHoldSymbolsStyle
mButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
mButtonForegroundStyle1:
  animation: animation
  text: M
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
mButtonUppercasedStateForegroundStyle1:
  animation: animation
  text: M
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
mButtonUpForegroundStyle:
  animation: animation
  text: "\u3002"
  center:
    x: 0.25
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
mButtonDownForegroundStyle:
  animation: animation
  text: "\uF4F0"
  center:
    x: 0.75
    y: 0.65
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
mButtonHintStyle:
  backgroundStyle: alphabeticHintBackgroundStyle
  foregroundStyle: mButtonHintForegroundStyle
  swipeUpForegroundStyle: mButtonSwipeUpHintForegroundStyle
  swipeDownForegroundStyle: mButtonSwipeDownHintForegroundStyle
mButtonHintForegroundStyle:
  text: M
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.25em
  normalColor: FFFFFF
mButtonSwipeUpHintForegroundStyle:
  text: .
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
mButtonSwipeDownHintForegroundStyle:
  text: "\uF4F0"
  center:
    x: 0.5
    y: 0.65
  fontSize: 1.125em
  normalColor: FFFFFF
mButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - mButtonHoldSymbolsForegroundStyle0
  - mButtonHoldSymbolsForegroundStyle1
  actions:
  - symbol: '*'
  - symbol: .
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
mButtonHoldSymbolsForegroundStyle0:
  text: '*'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
mButtonHoldSymbolsForegroundStyle1:
  text: .
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
backspaceButton:
  size:
    width: 200/784
  bounds:
    width: 190/200
    alignment: right
  backgroundStyle: backspaceButtonBackgroundStyle
  foregroundStyle:
  - backspaceButtonForegroundStyle
  action: backspace
  repeatAction: backspace
  swipeLeftAction:
    shortcutCommand: "#\u91CD\u8F93"
  swipeUpAction:
    shortcutCommand: '#deleteText'
  swipeDownAction:
    shortcutCommand: '#deleteText'
backspaceButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
backspaceButtonForegroundStyle:
  animation: animation
  systemImageName: delete.left
  center:
    x: 0.5
    y: 0.5
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
symButton:
  size:
    width: 292/784
  backgroundStyle: symButtonBackgroundStyle
  foregroundStyle:
  - symButtonForegroundStyle
  action:
    keyboardType: symbolic
  swipeUpAction:
    keyboardType: emoji
  swipeDownAction:
    keyboardType: emoji
symButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
symButtonForegroundStyle:
  animation: animation
  text: "\u7B26"
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
dotButton:
  size:
    width: 146/784
  backgroundStyle: dotButtonBackgroundStyle
  foregroundStyle:
  - dotButtonForegroundStyle
  - dotButtonUpForegroundStyle
  action:
    character: ','
  swipeUpAction:
    character: .
  swipeDownAction:
    character: .
  holdSymbolsStyle: dotButtonHoldSymbolsStyle
dotButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
dotButtonForegroundStyle:
  animation: animation
  text: ','
  center:
    x: 0.5
    y: 0.75
  fontSize: 1em
  normalColor: 070707
  highlightColor: 070707
dotButtonUpForegroundStyle:
  animation: animation
  text: "\u3002"
  center:
    x: 0.58
    y: 0.5
  fontSize: 0.94em
  normalColor: 070707
  highlightColor: 070707
dotButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - dotButtonHoldSymbolsForegroundStyle0
  - dotButtonHoldSymbolsForegroundStyle1
  actions:
  - sendKeys: Shift+Control+j
  - sendKeys: Control+j
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 1
dotButtonHoldSymbolsForegroundStyle0:
  text: "\u51E0\u91CD"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
dotButtonHoldSymbolsForegroundStyle1:
  text: "\u62C6\u5206"
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
spaceButton:
  size:
    width: 346/784
  backgroundStyle: spaceButtonBackgroundStyle
  foregroundStyle: spaceButtonForegroundStyle
  preeditStateForegroundStyle: spaceButtonForegroundStyle1
  action: space
  swipeUpAction:
    shortcutCommand: "#\u6B21\u9009\u4E0A\u5C4F"
  swipeDownAction:
    shortcutCommand: "#\u4E09\u9009\u4E0A\u5C4F"
spaceButtonBackgroundStyle:
  animation: animation
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  type: original
  normalColor: FFFFFF
  highlightColor: FFFFFF
  cornerRadius: 6
  normalLowerEdgeColor: B3B3B3
  highlightLowerEdgeColor: B3B3B3
spaceButtonForegroundStyle:
  animation: animation
  systemImageName: space
  center:
    x: 0.5
    y: 0.55
  fontSize: 1.25em
  normalColor: 070707
  highlightColor: 070707
spaceButtonForegroundStyle1:
  animation: animation
  text: |-
    // JavaScript
    function getText() {
      const candidate = $getRimeCandidates()
      if (candidate && candidate.length > 0) {
        return candidate[0].text;
      }
      const preedit = $getRimePreedit();
      if (preedit) {
          return preedit;
      }
    }
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
cnenButton:
  size:
    width: 146/784
  backgroundStyle: cnenButtonBackgroundStyle
  foregroundStyle:
  - cnenButtonForegroundStyle1
  - cnenButtonForegroundStyle2
  action:
    keyboardType: alphabetic
  swipeUpAction:
    shortcutCommand: "#\u4E2D\u82F1\u5207\u6362"
  swipeDownAction:
    shortcutCommand: "#\u7B80\u7E41\u5207\u6362"
  holdSymbolsStyle: cnenButtonHoldSymbolsStyle
cnenButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
cnenButtonForegroundStyle1:
  animation: animation
  center:
    x: 0.38
    y: 0.73
  text: "\u4E2D"
  fontSize: 0.81em
  normalColor: 070707
  highlightColor: 070707
cnenButtonForegroundStyle2:
  animation: animation
  center:
    x: 0.65
    y: 0.97
  text: "/\u82F1"
  fontSize: 0.5em
  normalColor: A7AEB9
  highlightColor: A7AEB9
cnenButtonHoldSymbolsStyle:
  backgroundStyle: alphabeticHoldSymbolsBackgroundStyle
  foregroundStyle:
  - cnenButtonHoldSymbolsForegroundStyle0
  - cnenButtonHoldSymbolsForegroundStyle1
  - cnenButtonHoldSymbolsForegroundStyle2
  - cnenButtonHoldSymbolsForegroundStyle3
  - cnenButtonHoldSymbolsForegroundStyle4
  - cnenButtonHoldSymbolsForegroundStyle5
  - cnenButtonHoldSymbolsForegroundStyle6
  - cnenButtonHoldSymbolsForegroundStyle7
  actions:
  - symbol: <
  - symbol: '['
  - symbol: '{'
  - symbol: (
  - symbol: )
  - symbol: '}'
  - symbol: ']'
  - symbol: '>'
  selectedStyle: alphabeticHoldSymbolsSelectedStyle
  selectedIndex: 3
cnenButtonHoldSymbolsForegroundStyle0:
  text: <
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cnenButtonHoldSymbolsForegroundStyle1:
  text: '['
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cnenButtonHoldSymbolsForegroundStyle2:
  text: '{'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cnenButtonHoldSymbolsForegroundStyle3:
  text: (
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cnenButtonHoldSymbolsForegroundStyle4:
  text: )
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cnenButtonHoldSymbolsForegroundStyle5:
  text: '}'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cnenButtonHoldSymbolsForegroundStyle6:
  text: ']'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
cnenButtonHoldSymbolsForegroundStyle7:
  text: '>'
  fontSize: 1em
  center:
    x: 0.5
    y: 0.58
  normalColor: 7DACFF
  highlightColor: FFFFFF
enterButton:
  size:
    width: 292/784
  backgroundStyle: enterButtonBackgroundStyle
  foregroundStyle:
  - enterButtonForegroundStyle
  action: enter
  swipeUpAction:
    shortcutCommand: "#\u6362\u884C"
  swipeDownAction:
    shortcutCommand: "#\u65B9\u6848\u5207\u6362"
enterButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: 3379f5
  highlightColor: 296bdf
  cornerRadius: 6
  normalLowerEdgeColor: 1d61d9
  highlightLowerEdgeColor: 296bdf
enterButtonForegroundStyle:
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  const type = $getReturnKeyType();\n
    \ switch (type) {\n    case 1:\n      return \"\u524D\u5F80\";\n    case 3:\n
    \     return \"\u52A0\u5165\";\n    case 4:\n      return \"\u524D\u5F80\";\n
    \   case 6:\n      return \"\u641C\u7D22\"\n    case 7:\n      return \"\u53D1\u9001\"\n
    \   case 9:\n      return \"\u5B8C\u6210\";\n    default:\n      return \"\u6362\u884C\";\n
    \ }\n}"
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: FFFFFF
  highlightColor: FFFFFF
collection:
  size:
    height: 3/4
  backgroundStyle: collectionBackgroundStyle
  type: symbols
  dataSource: symbols
  cellStyle: collectionCellStyle
collectionBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
collectionCellStyle:
  backgroundStyle: collectionCellBackgroundStyle
  foregroundStyle: collectionCellForegroundStyle
collectionCellBackgroundStyle:
  type: original
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  normalColor: FFFFFFBB
  cornerRadius: 6
collectionCellForegroundStyle:
  normalColor: 070707
  fontSize: 0.875em
1Button:
  backgroundStyle: 1ButtonBackgroundStyle
  foregroundStyle:
  - 1ButtonForegroundStyle
  action:
    character: 1
1ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
1ButtonForegroundStyle:
  animation: animation
  text: '1'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
4Button:
  backgroundStyle: 4ButtonBackgroundStyle
  foregroundStyle:
  - 4ButtonForegroundStyle
  action:
    character: 4
4ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
4ButtonForegroundStyle:
  animation: animation
  text: '4'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
7Button:
  backgroundStyle: 7ButtonBackgroundStyle
  foregroundStyle:
  - 7ButtonForegroundStyle
  action:
    character: 7
7ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
7ButtonForegroundStyle:
  animation: animation
  text: '7'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
2Button:
  backgroundStyle: 2ButtonBackgroundStyle
  foregroundStyle:
  - 2ButtonForegroundStyle
  action:
    character: 2
2ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
2ButtonForegroundStyle:
  animation: animation
  text: '2'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
5Button:
  backgroundStyle: 5ButtonBackgroundStyle
  foregroundStyle:
  - 5ButtonForegroundStyle
  action:
    character: 5
5ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
5ButtonForegroundStyle:
  animation: animation
  text: '5'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
8Button:
  backgroundStyle: 8ButtonBackgroundStyle
  foregroundStyle:
  - 8ButtonForegroundStyle
  action:
    character: 8
8ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
8ButtonForegroundStyle:
  animation: animation
  text: '8'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
0Button:
  backgroundStyle: 0ButtonBackgroundStyle
  foregroundStyle:
  - 0ButtonForegroundStyle
  action:
    character: 0
0ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
0ButtonForegroundStyle:
  animation: animation
  text: '0'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
3Button:
  backgroundStyle: 3ButtonBackgroundStyle
  foregroundStyle:
  - 3ButtonForegroundStyle
  action:
    character: 3
3ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
3ButtonForegroundStyle:
  animation: animation
  text: '3'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
6Button:
  backgroundStyle: 6ButtonBackgroundStyle
  foregroundStyle:
  - 6ButtonForegroundStyle
  action:
    character: 6
6ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
6ButtonForegroundStyle:
  animation: animation
  text: '6'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
9Button:
  backgroundStyle: 9ButtonBackgroundStyle
  foregroundStyle:
  - 9ButtonForegroundStyle
  action:
    character: 9
9ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  normalColor: FFFFFF
  highlightColor: E3E4E9
  cornerRadius: 6
  normalLowerEdgeColor: AEAEAE
  highlightLowerEdgeColor: 26262626
9ButtonForegroundStyle:
  animation: animation
  text: '9'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.9em
  normalColor: 070707
  highlightColor: 070707
periodButton:
  backgroundStyle: periodButtonBackgroundStyle
  foregroundStyle: periodButtonForegroundStyle
  action:
    character: .
periodButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
periodButtonForegroundStyle:
  animation: animation
  text: "\xB7"
  center:
    x: 0.5
    y: 0.75
  fontSize: 1.125em
  normalColor: 070707
  highlightColor: 070707
equalButton:
  size:
    height: 1/4
  backgroundStyle: equalButtonBackgroundStyle
  foregroundStyle: equalButtonForegroundStyle
  action:
    character: '='
equalButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
equalButtonForegroundStyle:
  animation: animation
  text: '='
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
atButton:
  backgroundStyle: atButtonBackgroundStyle
  foregroundStyle: atButtonForegroundStyle
  action:
    character: '@'
atButtonBackgroundStyle:
  insets:
    top: 3
    left: 3
    bottom: 3
    right: 3
  animation: animation
  type: original
  normalColor: E3E4E9
  highlightColor: 93939744
  cornerRadius: 6
  normalLowerEdgeColor: 26262626
  highlightLowerEdgeColor: 17171714
atButtonForegroundStyle:
  animation: animation
  text: '@'
  center:
    x: 0.5
    y: 0.75
  fontSize: 0.88em
  normalColor: 070707
  highlightColor: 070707
dataSource:
  symbols:
  - +
  - '-'
  - '*'
  - /
  - ()
  - ','
  - '#'
  - '%'
  - ':'
  - _
  - '?'
  - "\uFFE5"
