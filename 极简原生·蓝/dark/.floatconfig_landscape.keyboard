"\u6309\u952E\u80CC\u666F":
  type: original
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
"\u6309\u952E\u4E0A\u6807\u524D\u666F\u6837\u5F0F":
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
"\u6309\u952E\u6587\u5B57\u524D\u666F\u6837\u5F0F":
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
keyboard:
  style: keyboardStyle
  subviews:
  - HStack:
      subviews:
      - Cell: hamsterButton
      - Cell: uploadButton
      - Cell: scriptButton
      - Cell: fileButton
  - HStack:
      subviews:
      - Cell: skinButton
      - Cell: soundsButton
      - Cell: backupButton
      - Cell: deployButton
floatTargetScale:
  x: 0.45
  y: 0.7
keyboardStyle:
  insets:
    top: 8
    left: 8
    bottom: 8
    right: 8
  backgroundStyle: keyboardBackgroundStyle
keyboardBackgroundStyle:
  type: original
  normalColor: 2D2D2DDD
  cornerRadius: 8
  borderSize: 0.5
  normalBorderColor: 22222255
  normalLowerEdgeColor: 55555555
hamsterButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - hamsterButtonForegroundStyle1
  - hamsterButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/main
hamsterButtonForegroundStyle1:
  animation: animation
  systemImageName: keyboard
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
hamsterButtonForegroundStyle2:
  animation: animation
  text: "\u8BBE\u7F6E"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
uploadButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - uploadButtonForegroundStyle1
  - uploadButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/uploadInputSchema
uploadButtonForegroundStyle1:
  animation: animation
  systemImageName: wifi
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
uploadButtonForegroundStyle2:
  animation: animation
  text: "\u4E0A\u4F20"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
soundsButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - soundsButtonForegroundStyle1
  - soundsButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/feedback
soundsButtonForegroundStyle1:
  animation: animation
  systemImageName: speaker.wave.2
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
soundsButtonForegroundStyle2:
  animation: animation
  text: "\u58F0\u97F3"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
scriptButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - scriptButtonForegroundStyle1
  - scriptButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/clipboard?type=script
scriptButtonForegroundStyle1:
  animation: animation
  systemImageName: terminal
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
scriptButtonForegroundStyle2:
  animation: animation
  text: "\u811A\u672C"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
skinButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - skinButtonForegroundStyle1
  - skinButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/keyboardSkins
skinButtonForegroundStyle1:
  animation: animation
  systemImageName: tshirt
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
skinButtonForegroundStyle2:
  animation: animation
  text: "\u76AE\u80A4"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
fileButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - fileButtonForegroundStyle1
  - fileButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/finder
fileButtonForegroundStyle1:
  animation: animation
  systemImageName: folder
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
fileButtonForegroundStyle2:
  animation: animation
  text: "\u6587\u4EF6"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
backupButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - backupButtonForegroundStyle1
  - backupButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/backup
backupButtonForegroundStyle1:
  animation: animation
  systemImageName: person.icloud
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
backupButtonForegroundStyle2:
  animation: animation
  text: "\u5907\u4EFD"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
deployButton:
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - deployButtonForegroundStyle1
  - deployButtonForegroundStyle2
  action:
    openURL: hamster://dev.fuxiao.app.hamster/rime?deploy
deployButtonForegroundStyle1:
  animation: animation
  systemImageName: arrow.2.circlepath
  fontSize: 0.88em
  center:
    y: 0.38
  normalColor: A7AEB9
  highlightColor: FFFFFF
deployButtonForegroundStyle2:
  animation: animation
  text: "\u90E8\u7F72"
  fontSize: 0.75em
  center:
    y: 1.05
  normalColor: FFFFFF
  highlightColor: FFFFFF
ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
animation:
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 1
  toScale: 0.8
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 0.8
  toScale: 1
