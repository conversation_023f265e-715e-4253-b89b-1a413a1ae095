预编辑区高度: &preedit_Height 26
工具栏高度: &toolbar_Height 45
键盘区高度: &keyboard_Height 210

编码字体大小: &FontSize_preedit 0.88em
横排序号字体大小: &FontSize_hpxh 1.19em
横排文字字体大小: &FontSize_hpwz 1.19em
横排注释字体大小: &FontSize_hpvu 0.81em
展开序号字体大小: &FontSize_xlxh 1.125em
展开文字字体大小: &FontSize_xlwz 1.125em
展开注释字体大小: &FontSize_xlvu 0.75em

编码色: &preedit_textColor FDFDFD
首选色: &textfirst_color 4c8dff
次选色: &text_Color FDFDFD

键盘底色: &bg_Color 

按键字符色: &key_textcolor1 FFFFFF
删除键字符色: &backspace_textcolor FFFFFF
角标色: &key_textcolor2 A7AEB9

状态栏图标缩放: &toolbarButton_targetScale 0.55

收起图标: &vtltb
  fontSize: 1em
  normalColor: *key_textcolor1
  highlightColor: *key_textcolor1

展开候选列表和详情栏背景色: &zkhxbj 

展开候选功能键字符样式: &zkhx
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: *key_textcolor1
  highlightColor: *key_textcolor1

列表文字样式: &lb
  normalColor: *key_textcolor1
  highlightColor: *key_textcolor1
  badgeNormalColor: *key_textcolor2
  fontSize: 0.875em

列表栏背景: &lb_bj
  type: original
  insets: {top: 5, left: 3, bottom: 5, right: 3}
  normalColor: 4a4d5080
  highlightColor: 4a4d5080
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818

列表栏高亮背景: &lb_hl
  type: original
  insets: {top: 5, left: 3, bottom: 5, right: 3}
  normalColor: 383838
  cornerRadius: 6

详情栏背景: &xql_bg
  type: original
  insets: {top: 5, left: 3, bottom: 5, right: 3}
  normalColor: *zkhxbj
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818

功能键背景: &Function_bg
  type: original
  insets: {top: 5, left: 3, bottom: 6, right: 3}
  normalColor: 4a4d5080
  highlightColor: 4a4d5080
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818

backspace背景: &backspace_bg
  type: original
  insets: {top: 5, left: 3, bottom: 6, right: 3}
  normalColor: 3379f599
  highlightColor: 2769dd8080
  cornerRadius: 9
  normalLowerEdgeColor: 222222
  highlightLowerEdgeColor: 222222

backspace前景: &backspace_qj
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: *backspace_textcolor
  highlightColor: *backspace_textcolor

功能键前景: &Function_qj
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: *key_textcolor1
  highlightColor: *key_textcolor1

#预编辑区背景
preeditBackgroundStyle:
  type: original
  normalColor: *bg_Color

#工具栏背景
toolbarBackgroundStyle:
  type: original
  normalColor: *bg_Color

#展开后选背景
verticalCandidateBackgroundStyle:
  type: original
  normalColor: *bg_Color

#键盘区背景
keyboardBackgroundStyle:
  type: original
  normalColor: *bg_Color

#按键背景动画
animation:
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 1
  toScale: 0.8
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 0.8
  toScale: 1

preeditHeight: *preedit_Height
toolbarHeight: *toolbar_Height
keyboardHeight: *keyboard_Height

preedit:
  insets: {left: 10, top: 2}
  backgroundStyle: preeditBackgroundStyle
  foregroundStyle: preeditForegroundStyle

preeditForegroundStyle:
  textColor: *preedit_textColor
  fontSize: *FontSize_preedit

toolbar:
  backgroundStyle: toolbarBackgroundStyle
  primaryButtonStyle: primaryButtonStyle
  secondaryButtonStyle:
  - toolbarButtonHideStyle
  - toolbarButton1Style
  - toolbarButton2Style
  - toolbarButton3Style
  - toolbarButton4Style
  - toolbarButton5Style
  - toolbarButton6Style
  horizontalCandidateStyle: horizontalCandidateStyle
  verticalCandidateStyle: verticalCandidateStyle

primaryButtonStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - primaryButtonForegroundStyle
  action: {floatKeyboardType: floatconfig}

primaryButtonForegroundStyle:
  normalImage:
    file: 设置
    image: IMG1
  highlightImage:
    file: 设置
    image: IMG1
  targetScale: *toolbarButton_targetScale

toolbarButtonHideStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButtonHideForegroundStyle
  action: dismissKeyboard

toolbarButtonHideForegroundStyle:
  normalImage:
    file: 收起
    image: IMG1
  highlightImage:
    file: 收起
    image: IMG1
  targetScale: *toolbarButton_targetScale

toolbarButton1Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton1ForegroundStyle
  action: {shortcutCommand: '#showPasteboardView'}

toolbarButton1ForegroundStyle:
  normalImage:
    file: 剪贴
    image: IMG1
  highlightImage:
    file: 剪贴
    image: IMG1
  targetScale: *toolbarButton_targetScale

toolbarButton2Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton2ForegroundStyle
  action: {shortcutCommand: '#showPhraseView'}

toolbarButton2ForegroundStyle:
  normalImage:
    file: 短语
    image: IMG1
  highlightImage:
    file: 短语
    image: IMG1
  targetScale: *toolbarButton_targetScale

horizontalCandidateStyle:
  insets: {left: 5, top: 5, bottom: 5}
  candidateStateButtonStyle: candidateStateButtonStyle
  highlightBackgroundColor: 00000000
  preferredBackgroundColor: 00000000
  preferredIndexColor: *textfirst_color
  preferredTextColor: *textfirst_color
  preferredCommentColor: *textfirst_color
  indexColor: *text_Color
  textColor: *text_Color
  commentColor: *text_Color
  indexFontSize: *FontSize_hpxh
  textFontSize: *FontSize_hpwz
  commentFontSize: *FontSize_hpvu
  itemSpacing: 5

candidateStateButtonStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle: candidateStateButtonForegroundStyle

candidateStateButtonForegroundStyle:
  systemImageName: chevron.down
  <<: *vtltb

verticalCandidateStyle:
  insets: {top: 3, bottom: 3, left: 4, right: 4}
  bottomRowHeight: 43
  backgroundStyle: verticalCandidateBackgroundStyle
  candidateStyle: verticalCandidateOfCandidateStyle
  pageUpButtonStyle: verticalCandidatePageUpButtonStyle
  pageDownButtonStyle: verticalCandidatePageDownButtonStyle
  returnButtonStyle: verticalCandidateReturnButtonStyle
  backspaceButtonStyle: verticalCandidateBackspaceButtonStyle

verticalCandidateOfCandidateStyle:
  insets: {top: 3, bottom: 6, left: 8, right: 8}
  cornerRadius: 9
  backgroundColor: *zkhxbj
  separatorColor: 00000000
  highlightBackgroundColor: 00000000
  preferredBackgroundColor: 00000000
  preferredIndexColor: *text_Color
  preferredTextColor: *text_Color
  preferredCommentColor: *text_Color
  indexColor: *text_Color
  textColor: *text_Color
  commentColor: *text_Color
  indexFontSize: *FontSize_xlxh
  textFontSize: *FontSize_xlwz
  commentFontSize: *FontSize_xlvu

verticalCandidatePageUpButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidatePageUpButtonForegroundStyle

verticalCandidateButtonBackgroundStyle:
  insets: {top: 3, left: 8, bottom: 3, right: 8}
  <<: *Function_bg

verticalCandidatePageUpButtonForegroundStyle:
  text: 
  <<: *zkhx

verticalCandidatePageDownButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidatePageDownButtonForegroundStyle

verticalCandidatePageDownButtonForegroundStyle:
  text: 
  <<: *zkhx

verticalCandidateReturnButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidateReturnButtonForegroundStyle

verticalCandidateReturnButtonForegroundStyle:
  text: 
  <<: *zkhx

verticalCandidateBackspaceButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle:
  - verticalCandidateBackspaceButtonForegroundStyle

verticalCandidateBackspaceButtonForegroundStyle:
  text: 
  <<: *zkhx

keyboard:
  style: keyboardStyle
  subviews:
  - HStack:
      subviews:
      - Cell: categoryCollection
      - Cell: descriptionCollection
  - HStack:
      style: HStackStyle
      subviews:
      - Cell: returnButton
      - Cell: pageUpButton
      - Cell: pageDownButton
      - Cell: lockButton
      - Cell: backspaceButton

keyboardStyle:
  insets: {top: 1}
  backgroundStyle: keyboardBackgroundStyle

HStackStyle:
  size:
    height: 2.2/10

categoryCollection:
  size:
    width: 60/375
  backgroundStyle: categoryCollectionButtonBackgroundStyle
  type: classifiedSymbols
  dataSource: category
  cellStyle: collectionCellStyle

categoryCollectionButtonBackgroundStyle:
  <<: *lb_bj

collectionCellStyle:
  backgroundStyle: collectionCellBackgroundStyle
  foregroundStyle: collectionCellForegroundStyle

collectionCellBackgroundStyle:
  <<: *lb_hl

collectionCellForegroundStyle:
  <<: *lb

#详情页
descriptionCollection:
  size:
    width: 315/375
  backgroundStyle: descriptionCollectionButtonBackgroundStyle
  type: subClassifiedSymbols
  cellStyle: descriptionCollectionStyle

descriptionCollectionButtonBackgroundStyle:
  <<: *xql_bg

descriptionCollectionStyle:
  backgroundStyle: collectionCellBackgroundStyle
  foregroundStyle: descriptionCollectionForegroundStyle

descriptionCollectionForegroundStyle:
  <<: *lb

returnButton:
  size:
    width: 60/375
  backgroundStyle: returnButtonBackgroundStyle
  foregroundStyle: returnButtonForegroundStyle
  action: returnPrimaryKeyboard

returnButtonBackgroundStyle:
  animation: animation
  <<: *Function_bg

returnButtonForegroundStyle:
  animation: animation
  text: 
  <<: *Function_qj

pageUpButton:
  size:
    width: 85/375
  backgroundStyle: systemButtonBackgroundStyle
  foregroundStyle: pageUpButtonForegroundStyle
  action: pageUp

systemButtonBackgroundStyle:
  animation: animation
  <<: *Function_bg

pageUpButtonForegroundStyle:
  animation: animation
  text: 
  <<: *Function_qj

pageDownButton:
  size:
    width: 85/375
  backgroundStyle: systemButtonBackgroundStyle
  foregroundStyle: pageDownButtonForegroundStyle
  action: pageDown

pageDownButtonForegroundStyle:
  animation: animation
  text: 
  <<: *Function_qj

lockButton:
  size:
    width: 85/375
  backgroundStyle: lockButtonBackgroundStyle
  foregroundStyle: |-
    // JavaScript
    function getText() {
      return $getSymbolicKeyboardLockState() ? "lockButtonForegroundStyle" : "unlockButtonForegroundStyle";
    }
  action: symbolicKeyboardLockStateToggle

lockButtonBackgroundStyle:
  animation: animation
  <<: *Function_bg

lockButtonForegroundStyle:
  animation: animation
  text: 
  <<: *Function_qj

unlockButtonForegroundStyle:
  animation: animation
  text: 
  <<: *Function_qj

backspaceButton:
  size:
    width: 60/375
  backgroundStyle: backspaceButtonBackgroundStyle
  foregroundStyle: backspaceButtonForegroundStyle
  action: backspace
  repeatAction: backspace

backspaceButtonBackgroundStyle:
  animation: animation
  <<: *backspace_bg

backspaceButtonForegroundStyle:
  animation: animation
  text: 
  <<: *backspace_qj

dataSource:
  category:
  - 常用
  - 表情
  - 人物
  - 动物
  - 植物
  - 饮食
  - 景观
  - 交通
  - 时间
  - 天气
  - 活动
  - 物品
  - 符号
  - 旗帜
  常用:
  - 😀
  - 😃
  - 😄
  - 😁
  - 😆
  - 😅
  - 🤣
  - 😂
  - 🙂
  表情:
  - 😀
  - 😃
  - 😄
  - 😁
  - 😆
  - 😅
  - 🤣
  - 😂
  - 🙂
  - 🙃
  - 🫠
  - 😉
  - 😊
  - 😇
  - 🥰
  - 😍
  - 🤩
  - 😘
  - 😗
  - ☺
  - 😚
  - 😙
  - 🥲
  - 😋
  - 😛
  - 😜
  - 🤪
  - 😝
  - 🤑
  - 🤗
  - 🤭
  - 🫢
  - 🫣
  - 🤫
  - 🤔
  - 🫡
  - 🤐
  - 🤨
  - 😐
  - 😑
  - 😶
  - 🫥
  - 😶‍🌫️
  - 😏
  - 😒
  - 🙄
  - 😬
  - 😮‍
  - 💨
  - 🤥
  - 🫨
  - 😌
  - 😔
  - 😪
  - 🤤
  - 😴
  - 😷
  - 🤒
  - 🤕
  - 🤢
  - 🤮
  - 🤧
  - 🥵
  - 🥶
  - 🥴
  - 😵
  - 😵‍
  - 💫
  - 🤯
  - 🤠
  - 🥳
  - 🥸
  - 😎
  - 🤓
  - 🧐
  - 😕
  - 🫤
  - 😟
  - 🙁
  - ☹
  - 😮
  - 😯
  - 😲
  - 😳
  - 🥺
  - 🥹
  - 😦
  - 😧
  - 😨
  - 😰
  - 😥
  - 😢
  - 😭
  - 😱
  - 😖
  - 😣
  - 😞
  - 😓
  - 😩
  - 😫
  - 🥱
  - 😤
  - 😡
  - 😠
  - 🤬
  - 😈
  - 👿
  - 💀
  - ☠
  - 💩
  - 🤡
  - 👹
  - 👺
  - 👻
  - 👽
  - 👾
  - 🤖
  - 😺
  - 😸
  - 😹
  - 😻
  - 😼
  - 😽
  - 🙀
  - 😿
  - 😾
  - 🙈
  - 🙉
  - 🙊
  - 💌
  - 💘
  - 💝
  - 💖
  - 💗
  - 💓
  - 💞
  - 💕
  - 💟
  - ❣
  - 💔
  - ❤️‍
  - 🔥
  - ❤️‍
  - 🩹
  - ❤️
  - 🩷
  - 🧡
  - 💛
  - 💚
  - 💙
  - 🩵
  - 💜
  - 🤎
  - 🖤
  - 🩶
  - 🤍
  - 💋
  - 💯
  - 💢
  - 💥
  - 💫
  - 💦
  - 💨
  - 🕳
  - 💬
  - 👁️‍🗨
  - 🗨
  - 🗯
  - 💭
  - 💤
  人物:
  - 👋
  - 🤚
  - 🖐️
  - ✋
  - 🖖
  - 🫱
  - 🫲
  - 🫳
  - 🫴
  - 🫷
  - 🫸
  - 👌
  - 🤌
  - 🤏
  - ✌️
  - 🤞
  - 🫰
  - 🤟
  - 🤘
  - 🤙
  - 👈
  - 👉
  - 👆
  - 🖕
  - 👇
  - ☝️
  - 🫵
  - 👍
  - 👎
  - ✊
  - 👊
  - 🤛
  - 🤜
  - 👏
  - 🙌
  - 🫶
  - 👐
  - 🤲
  - 🤝
  - 🙏
  - ✍️
  - 💅
  - 🤳
  - 💪
  - 🦾
  - 🦿
  - 🦵
  - 🦶
  - 👂
  - 🦻
  - 👃
  - 🧠
  - 🫀
  - 🫁
  - 🦷
  - 🦴
  - 👀
  - 👁️
  - 👅
  - 👄
  - 🫦
  - 👶
  - 🧒
  - 👦
  - 👧
  - 🧑
  - 👱
  - 👨
  - 🧔
  - 🧔‍♂️
  - 🧔‍♀️
  - 👨‍🦰
  - 👨‍🦱
  - 👨‍🦳
  - 👨‍🦲
  - 👩
  - 👩‍🦰
  - 🧑‍🦰
  - 👩‍🦱
  - 🧑‍🦱
  - 👩‍🦳
  - 🧑‍🦳
  - 👩‍🦲
  - 🧑‍🦲
  - 👱‍♀️
  - 👱‍♂️
  - 🧓
  - 👴
  - 👵
  - 🙍
  - 🙍‍♂️
  - 🙍‍♀️
  - 🙎
  - 🙎‍♂️
  - 🙎‍♀️
  - 🙅
  - 🙅‍♂️
  - 🙅‍♀️
  - 🙆
  - 🙆‍♂️
  - 🙆‍♀️
  - 💁
  - 💁‍♂️
  - 💁‍♀️
  - 🙋
  - 🙋‍♂️
  - 🙋‍♀️
  - 🧏
  - 🧏‍♂️
  - 🧏‍♀️
  - 🙇
  - 🙇‍♂️
  - 🙇‍♀️
  - 🤦
  - 🤦‍♂️
  - 🤦‍♀️
  - 🤷
  - 🤷‍♂️
  - 🤷‍♀️
  - 🧑‍⚕️
  - 👨‍⚕️
  - 👩‍⚕️
  - 🧑‍🎓
  - 👨‍🎓
  - 👩‍🎓
  - 🧑‍🏫
  - 👨‍🏫
  - 👩‍🏫
  - 🧑‍⚖️
  - 👨‍⚖️
  - 👩‍⚖️
  - 🧑‍🌾
  - 👨‍🌾
  - 👩‍🌾
  - 🧑‍🍳
  - 👨‍🍳
  - 👩‍🍳
  - 🧑‍🔧
  - 👨‍🔧
  - 👩‍🔧
  - 🧑‍🏭
  - 👨‍🏭
  - 👩‍🏭
  - 🧑‍💼
  - 👨‍💼
  - 👩‍💼
  - 🧑‍🔬
  - 👨‍🔬
  - 👩‍🔬
  - 🧑‍💻
  - 👨‍💻
  - 👩‍💻
  - 🧑‍🎤
  - 👨‍🎤
  - 👩‍🎤
  - 🧑‍🎨
  - 👨‍🎨
  - 👩‍🎨
  - 🧑‍✈️
  - 👨‍✈️
  - 👩‍✈️
  - 🧑‍🚀
  - 👨‍🚀
  - 👩‍🚀
  - 🧑‍🚒
  - 👨‍🚒
  - 👩‍🚒
  - 👮
  - 👮‍♂️
  - 👮‍♀️
  - 🕵️
  - 🕵️‍♂️
  - 🕵️‍♀️
  - 💂
  - 💂‍♂️
  - 💂‍♀️
  - 🥷
  - 👷
  - 👷‍♂️
  - 👷‍♀️
  - 🫅
  - 🤴
  - 👸
  - 👳
  - 👳‍♂️
  - 👳‍♀️
  - 👲
  - 🧕
  - 🤵
  - 🤵‍♂️
  - 🤵‍♀️
  - 👰
  - 👰‍♂️
  - 👰‍♀️
  - 🤰
  - 🫃
  - 🫄
  - 🤱
  - 👩‍🍼
  - 👨‍🍼
  - 🧑‍🍼
  - 👼
  - 🎅
  - 🤶
  - 🧑‍🎄
  - 🦸
  - 🦸‍♂️
  - 🦸‍♀️
  - 🦹
  - 🦹‍♂️
  - 🦹‍♀️
  - 🧙
  - 🧙‍♂️
  - 🧙‍♀️
  - 🧚
  - 🧚‍♂️
  - 🧚‍♀️
  - 🧛
  - 🧛‍♂️
  - 🧛‍♀️
  - 🧜
  - 🧜‍♂️
  - 🧜‍♀️
  - 🧝
  - 🧝‍♂️
  - 🧝‍♀️
  - 🧞
  - 🧞‍♂️
  - 🧞‍♀️
  - 🧟
  - 🧟‍♂️
  - 🧟‍♀️
  - 🧌
  - 💆
  - 💆‍♂️
  - 💆‍♀️
  - 💇
  - 💇‍♂️
  - 💇‍♀️
  - 🚶
  - 🚶‍♂️
  - 🚶‍♀️
  - 🧍
  - 🧍‍♂️
  - 🧍‍♀️
  - 🧎
  - 🧎‍♂️
  - 🧎‍♀️
  - 🧑‍🦯
  - 👨‍🦯
  - 👩‍🦯
  - 🧑‍🦼
  - 👨‍🦼
  - 👩‍🦼
  - 🧑‍🦽
  - 👨‍🦽
  - 👩‍🦽
  - 🏃
  - 🏃‍♂️
  - 🏃‍♀️
  - 💃
  - 🕺
  - 🕴️
  - 👯
  - 👯‍♂️
  - 👯‍♀️
  - 🧖
  - 🧖‍♂️
  - 🧖‍♀️
  - 🧗
  - 🧗‍♂️
  - 🧗‍♀️
  - 🤺
  - 🏇
  - ⛷️
  - 🏂
  - 🏌️
  - 🏌️‍♂️
  - 🏌️‍♀️
  - 🏄
  - 🏄‍♂️
  - 🏄‍♀️
  - 🚣
  - 🚣‍♂️
  - 🚣‍♀️
  - 🏊
  - 🏊‍♂️
  - 🏊‍♀️
  - ⛹️
  - ⛹️‍♂️
  - ⛹️‍♀️
  - 🏋️
  - 🏋️‍♂️
  - 🏋️‍♀️
  - 🚴
  - 🚴‍♂️
  - 🚴‍♀️
  - 🚵
  - 🚵‍♂️
  - 🚵‍♀️
  - 🤸
  - 🤸‍♂️
  - 🤸‍♀️
  - 🤼
  - 🤼‍♂️
  - 🤼‍♀️
  - 🤽
  - 🤽‍♂️
  - 🤽‍♀️
  - 🤾
  - 🤾‍♂️
  - 🤾‍♀️
  - 🤹
  - 🤹‍♂️
  - 🤹‍♀️
  - 🧘
  - 🧘‍♂️
  - 🧘‍♀️
  - 🛀
  - 🛌
  - 🧑‍🤝‍🧑
  - 👭
  - 👫
  - 👬
  - 💏
  - 👩‍❤️‍💋‍👨
  - 👨‍❤️‍💋‍👨
  - 👩‍❤️‍💋‍👩
  - 💑
  - 👩‍❤️‍👨
  - 👨‍❤️‍👨
  - 👩‍❤️‍👩
  - 👪
  - 👨‍👩‍👦
  - 👨‍👩‍👧
  - 👨‍👩‍👧‍👦
  - 👨‍👩‍👦‍👦
  - 👨‍👩‍👧‍👧
  - 👨‍👨‍👦
  - 👨‍👨‍👧
  - 👨‍👨‍👧‍👦
  - 👨‍👨‍👦‍👦
  - 👨‍👨‍👧‍👧
  - 👩‍👩‍👦
  - 👩‍👩‍👧
  - 👩‍👩‍👧‍👦
  - 👩‍👩‍👦‍👦
  - 👩‍👩‍👧‍👧
  - 👨‍👦
  - 👨‍👦‍👦
  - 👨‍👧
  - 👨‍👧‍👦
  - 👨‍👧‍👧
  - 👩‍👦
  - 👩‍👦‍👦
  - 👩‍👧
  - 👩‍👧‍👦
  - 👩‍👧‍👧
  - 🗣️
  - 👤
  - 👥
  - 🫂
  - 👣
  动物:
  - 🐵
  - 🐒
  - 🦍
  - 🦧
  - 🐶
  - 🐕
  - 🦮
  - 🐕‍🦺
  - 🐩
  - 🐺
  - 🦊
  - 🦝
  - 🐱
  - 🐈
  - 🐈‍⬛
  - 🦁
  - 🐯
  - 🐅
  - 🐆
  - 🐴
  - 🫎
  - 🫏
  - 🐎
  - 🦄
  - 🦓
  - 🦌
  - 🦬
  - 🐮
  - 🐂
  - 🐃
  - 🐄
  - 🐷
  - 🐖
  - 🐗
  - 🐽
  - 🐏
  - 🐑
  - 🐐
  - 🐪
  - 🐫
  - 🦙
  - 🦒
  - 🐘
  - 🦣
  - 🦏
  - 🦛
  - 🐭
  - 🐁
  - 🐀
  - 🐹
  - 🐰
  - 🐇
  - 🐿️
  - 🦫
  - 🦔
  - 🦇
  - 🐻
  - 🐻‍❄️
  - 🐨
  - 🐼
  - 🦥
  - 🦦
  - 🦨
  - 🦘
  - 🦡
  - 🐾
  - 🦃
  - 🐔
  - 🐓
  - 🐣
  - 🐤
  - 🐥
  - 🐦
  - 🐧
  - 🕊️
  - 🦅
  - 🦆
  - 🦢
  - 🦉
  - 🦤
  - 🪶
  - 🦩
  - 🦚
  - 🦜
  - 🪽
  - 🐦‍⬛
  - 🪿
  - 🐸
  - 🐊
  - 🐢
  - 🦎
  - 🐍
  - 🐲
  - 🐉
  - 🦕
  - 🦖
  - 🐳
  - 🐋
  - 🐬
  - 🦭
  - 🐟
  - 🐠
  - 🐡
  - 🦈
  - 🐙
  - 🐚
  - 🪸
  - 🪼
  - 🐌
  - 🦋
  - 🐛
  - 🐜
  - 🐝
  - 🪲
  - 🐞
  - 🦗
  - 🪳
  - 🕷️
  - 🕸️
  - 🦂
  - 🦟
  - 🪰
  - 🪱
  - 🦠
  植物:
  - 💐
  - 🌸
  - 💮
  - 🪷
  - 🏵️
  - 🌹
  - 🥀
  - 🌺
  - 🌻
  - 🌼
  - 🌷
  - 🪻
  - 🌱
  - 🪴
  - 🌲
  - 🌳
  - 🌴
  - 🌵
  - 🌾
  - 🌿
  - ☘️
  - 🍀
  - 🍁
  - 🍂
  - 🍃
  - 🪹
  - 🪺
  - 🍄
  饮食:
  - 🍇
  - 🍈
  - 🍉
  - 🍊
  - 🍋
  - 🍌
  - 🍍
  - 🥭
  - 🍎
  - 🍏
  - 🍐
  - 🍑
  - 🍒
  - 🍓
  - 🫐
  - 🥝
  - 🍅
  - 🫒
  - 🥥
  - 🥑
  - 🍆
  - 🥔
  - 🥕
  - 🌽
  - 🌶️
  - 🫑
  - 🥒
  - 🥬
  - 🥦
  - 🧄
  - 🧅
  - 🥜
  - 🫘
  - 🌰
  - 🫚
  - 🫛
  - 🍞
  - 🥐
  - 🥖
  - 🫓
  - 🥨
  - 🥯
  - 🥞
  - 🧇
  - 🧀
  - 🍖
  - 🍗
  - 🥩
  - 🥓
  - 🍔
  - 🍟
  - 🍕
  - 🌭
  - 🥪
  - 🌮
  - 🌯
  - 🫔
  - 🥙
  - 🧆
  - 🥚
  - 🍳
  - 🥘
  - 🍲
  - 🫕
  - 🥣
  - 🥗
  - 🍿
  - 🧈
  - 🧂
  - 🥫
  - 🍱
  - 🍘
  - 🍙
  - 🍚
  - 🍛
  - 🍜
  - 🍝
  - 🍠
  - 🍢
  - 🍣
  - 🍤
  - 🍥
  - 🥮
  - 🍡
  - 🥟
  - 🥠
  - 🥡
  - 🦀
  - 🦞
  - 🦐
  - 🦑
  - 🦪
  - 🍦
  - 🍧
  - 🍨
  - 🍩
  - 🍪
  - 🎂
  - 🍰
  - 🧁
  - 🥧
  - 🍫
  - 🍬
  - 🍭
  - 🍮
  - 🍯
  - 🍼
  - 🥛
  - ☕
  - 🫖
  - 🍵
  - 🍶
  - 🍾
  - 🍷
  - 🍸
  - 🍹
  - 🍺
  - 🍻
  - 🥂
  - 🥃
  - 🫗
  - 🥤
  - 🧋
  - 🧃
  - 🧉
  - 🧊
  - 🥢
  - 🍽️
  - 🍴
  - 🥄
  - 🔪
  - 🫙
  - 🏺
  景观:
  - 🌍
  - 🌎
  - 🌏
  - 🌐
  - 🗺️
  - 🗾
  - 🧭
  - 🏔️
  - ⛰️
  - 🌋
  - 🗻
  - 🏕️
  - 🏖️
  - 🏜️
  - 🏝️
  - 🏞️
  - 🏟️
  - 🏛️
  - 🏗️
  - 🧱
  - 🪨
  - 🪵
  - 🛖
  - 🏘️
  - 🏚️
  - 🏠
  - 🏡
  - 🏢
  - 🏣
  - 🏤
  - 🏥
  - 🏦
  - 🏨
  - 🏩
  - 🏪
  - 🏫
  - 🏬
  - 🏭
  - 🏯
  - 🏰
  - 💒
  - 🗼
  - 🗽
  - ⛪
  - 🕌
  - 🛕
  - 🕍
  - ⛩️
  - 🕋
  - ⛲
  - ⛺
  - 🌁
  - 🌃
  - 🏙️
  - 🌄
  - 🌅
  - 🌆
  - 🌇
  - 🌉
  - ♨️
  - 🎠
  - 🛝
  - 🎡
  - 🎢
  - 💈
  - 🎪
  交通:
  - 🚂
  - 🚃
  - 🚄
  - 🚅
  - 🚆
  - 🚇
  - 🚈
  - 🚉
  - 🚊
  - 🚝
  - 🚞
  - 🚋
  - 🚌
  - 🚍
  - 🚎
  - 🚐
  - 🚑
  - 🚒
  - 🚓
  - 🚔
  - 🚕
  - 🚖
  - 🚗
  - 🚘
  - 🚙
  - 🛻
  - 🚚
  - 🚛
  - 🚜
  - 🏎️
  - 🏍️
  - 🛵
  - 🦽
  - 🦼
  - 🛺
  - 🚲
  - 🛴
  - 🛹
  - 🛼
  - 🚏
  - 🛣️
  - 🛤️
  - 🛢️
  - ⛽
  - 🛞
  - 🚨
  - 🚥
  - 🚦
  - 🛑
  - 🚧
  - ⚓
  - 🛟
  - ⛵
  - 🛶
  - 🚤
  - 🛳️
  - ⛴️
  - 🛥️
  - 🚢
  - ✈️
  - 🛩️
  - 🛫
  - 🛬
  - 🪂
  - 💺
  - 🚁
  - 🚟
  - 🚠
  - 🚡
  - 🛰️
  - 🚀
  - 🛸
  时间:
  - 🛎️
  - 🧳
  - ⌛
  - ⏳
  - ⌚
  - ⏰
  - ⏱️
  - ⏲️
  - 🕰️
  - 🕛
  - 🕧
  - 🕐
  - 🕜
  - 🕑
  - 🕝
  - 🕒
  - 🕞
  - 🕓
  - 🕟
  - 🕔
  - 🕠
  - 🕕
  - 🕡
  - 🕖
  - 🕢
  - 🕗
  - 🕣
  - 🕘
  - 🕤
  - 🕙
  - 🕥
  - 🕚
  - 🕦
  - 🌑
  - 🌒
  - 🌓
  - 🌔
  - 🌕
  - 🌖
  - 🌗
  - 🌘
  - 🌙
  - 🌚
  - 🌛
  - 🌜
  - 🌡️
  - ☀️
  - 🌝
  - 🌞
  - 🪐
  - ⭐
  - 🌟
  - 🌠
  - 🌌
  天气:
  - ☁️
  - ⛅
  - ⛈️
  - 🌤️
  - 🌥️
  - 🌦️
  - 🌧️
  - 🌨️
  - 🌩️
  - 🌪️
  - 🌫️
  - 🌬️
  - 🌀
  - 🌈
  - 🌂
  - ☂️
  - ☔
  - ⛱️
  - ⚡
  - ❄️
  - ☃️
  - ⛄
  - ☄️
  - 🔥
  - 💧
  - 🌊
  活动:
  - 🎃
  - 🎄
  - 🎆
  - 🎇
  - 🧨
  - ✨
  - 🎈
  - 🎉
  - 🎊
  - 🎋
  - 🎍
  - 🎎
  - 🎏
  - 🎐
  - 🎑
  - 🧧
  - 🎀
  - 🎁
  - 🎗️
  - 🎟️
  - 🎫
  - 🎖️
  - 🏆
  - 🏅
  - 🥇
  - 🥈
  - 🥉
  - ⚽
  - ⚾
  - 🥎
  - 🏀
  - 🏐
  - 🏈
  - 🏉
  - 🎾
  - 🥏
  - 🎳
  - 🏏
  - 🏑
  - 🏒
  - 🥍
  - 🏓
  - 🏸
  - 🥊
  - 🥋
  - 🥅
  - ⛳
  - ⛸️
  - 🎣
  - 🤿
  - 🎽
  - 🎿
  - 🛷
  - 🥌
  - 🎯
  - 🪀
  - 🪁
  - 🔫
  - 🎱
  - 🔮
  - 🪄
  - 🎮
  - 🕹️
  - 🎰
  - 🎲
  - 🧩
  - 🧸
  - 🪅
  - 🪩
  - 🪆
  - ♠️
  - ♥️
  - ♦️
  - ♣️
  - ♟️
  - 🃏
  - 🀄
  - 🎴
  - 🎭
  - 🖼️
  - 🎨
  - 🧵
  - 🪡
  - 🧶
  - 🪢
  物品:
  - 👓
  - 🕶️
  - 🥽
  - 🥼
  - 🦺
  - 👔
  - 👕
  - 👖
  - 🧣
  - 🧤
  - 🧥
  - 🧦
  - 👗
  - 👘
  - 🥻
  - 🩱
  - 🩲
  - 🩳
  - 👙
  - 👚
  - 🪭
  - 👛
  - 👜
  - 👝
  - 🛍️
  - 🎒
  - 🩴
  - 👞
  - 👟
  - 🥾
  - 🥿
  - 👠
  - 👡
  - 🩰
  - 👢
  - 🪮
  - 👑
  - 👒
  - 🎩
  - 🎓
  - 🧢
  - 🪖
  - ⛑️
  - 📿
  - 💄
  - 💍
  - 💎
  - 🔇
  - 🔈
  - 🔉
  - 🔊
  - 📢
  - 📣
  - 📯
  - 🔔
  - 🔕
  - 🎼
  - 🎵
  - 🎶
  - 🎙️
  - 🎚️
  - 🎛️
  - 🎤
  - 🎧
  - 📻
  - 🎷
  - 🪗
  - 🎸
  - 🎹
  - 🎺
  - 🎻
  - 🪕
  - 🥁
  - 🪘
  - 🪇
  - 🪈
  - 📱
  - 📲
  - ☎️
  - 📞
  - 📟
  - 📠
  - 🔋
  - 🪫
  - 🔌
  - 💻
  - 🖥️
  - 🖨️
  - ⌨️
  - 🖱️
  - 🖲️
  - 💽
  - 💾
  - 💿
  - 📀
  - 🧮
  - 🎥
  - 🎞️
  - 📽️
  - 🎬
  - 📺
  - 📷
  - 📸
  - 📹
  - 📼
  - 🔍
  - 🔎
  - 🕯️
  - 💡
  - 🔦
  - 🏮
  - 🪔
  - 📔
  - 📕
  - 📖
  - 📗
  - 📘
  - 📙
  - 📚
  - 📓
  - 📒
  - 📃
  - 📜
  - 📄
  - 📰
  - 🗞️
  - 📑
  - 🔖
  - 🏷️
  - 💰
  - 🪙
  - 💴
  - 💵
  - 💶
  - 💷
  - 💸
  - 💳
  - 🧾
  - 💹
  - ✉️
  - 📧
  - 📨
  - 📩
  - 📤
  - 📥
  - 📦
  - 📫
  - 📪
  - 📬
  - 📭
  - 📮
  - 🗳️
  - ✏️
  - ✒️
  - 🖋️
  - 🖊️
  - 🖌️
  - 🖍️
  - 📝
  - 💼
  - 📁
  - 📂
  - 🗂️
  - 📅
  - 📆
  - 🗒️
  - 🗓️
  - 📇
  - 📈
  - 📉
  - 📊
  - 📋
  - 📌
  - 📍
  - 📎
  - 🖇️
  - 📏
  - 📐
  - ✂️
  - 🗃️
  - 🗄️
  - 🗑️
  - 🔒
  - 🔓
  - 🔏
  - 🔐
  - 🔑
  - 🗝️
  - 🔨
  - 🪓
  - ⛏️
  - ⚒️
  - 🛠️
  - 🗡️
  - ⚔️
  - 💣
  - 🪃
  - 🏹
  - 🛡️
  - 🪚
  - 🔧
  - 🪛
  - 🔩
  - ⚙️
  - 🗜️
  - ⚖️
  - 🦯
  - 🔗
  - ⛓️
  - 🪝
  - 🧰
  - 🧲
  - 🪜
  - ⚗️
  - 🧪
  - 🧫
  - 🧬
  - 🔬
  - 🔭
  - 📡
  - 💉
  - 🩸
  - 💊
  - 🩹
  - 🩼
  - 🩺
  - 🩻
  - 🚪
  - 🛗
  - 🪞
  - 🪟
  - 🛏️
  - 🛋️
  - 🪑
  - 🚽
  - 🪠
  - 🚿
  - 🛁
  - 🪤
  - 🪒
  - 🧴
  - 🧷
  - 🧹
  - 🧺
  - 🧻
  - 🪣
  - 🧼
  - 🫧
  - 🪥
  - 🧽
  - 🧯
  - 🛒
  - 🚬
  - ⚰️
  - 🪦
  - ⚱️
  - 🧿
  - 🪬
  - 🗿
  - 🪧
  - 🪪
  符号:
  - 🏧
  - 🚮
  - 🚰
  - ♿
  - 🚹
  - 🚺
  - 🚻
  - 🚼
  - 🚾
  - 🛂
  - 🛃
  - 🛄
  - 🛅
  - ⚠️
  - 🚸
  - ⛔
  - 🚫
  - 🚳
  - 🚭
  - 🚯
  - 🚱
  - 🚷
  - 📵
  - 🔞
  - ☢️
  - ☣️
  - ⬆️
  - ↗️
  - ➡️
  - ↘️
  - ⬇️
  - ↙️
  - ⬅️
  - ↖️
  - ↕️
  - ↔️
  - ↩️
  - ↪️
  - ⤴️
  - ⤵️
  - 🔃
  - 🔄
  - 🔙
  - 🔚
  - 🔛
  - 🔜
  - 🔝
  - 🛐
  - ⚛️
  - 🕉️
  - ✡️
  - ☸️
  - ☯️
  - ✝️
  - ☦️
  - ☪️
  - ☮️
  - 🕎
  - 🔯
  - 🪯
  - ♈
  - ♉
  - ♊
  - ♋
  - ♌
  - ♍
  - ♎
  - ♏
  - ♐
  - ♑
  - ♒
  - ♓
  - ⛎
  - 🔀
  - 🔁
  - 🔂
  - ▶️
  - ⏩
  - ⏭️
  - ⏯️
  - ◀️
  - ⏪
  - ⏮️
  - 🔼
  - ⏫
  - 🔽
  - ⏬
  - ⏸️
  - ⏹️
  - ⏺️
  - ⏏️
  - 🎦
  - 🔅
  - 🔆
  - 📶
  - 🛜
  - 📳
  - 📴
  - ♀️
  - ♂️
  - ⚧️
  - ✖️
  - ➕
  - ➖
  - ➗
  - 🟰
  - ♾️
  - '!!️'
  - '!?️'
  - ❓
  - ❔
  - ❕
  - ❗
  - 〰️
  - 💱
  - 💲
  - ⚕️
  - ♻️
  - ⚜️
  - 🔱
  - 📛
  - 🔰
  - ⭕
  - ✅
  - ☑️
  - ✔️
  - ❌
  - ❎
  - ➰
  - ➿
  - 〽️
  - ✳️
  - ✴️
  - ❇️
  - ©️
  - ®️
  - TM️
  - '#️⃣'
  - '*️⃣'
  - 0️⃣
  - 1️⃣
  - 2️⃣
  - 3️⃣
  - 4️⃣
  - 5️⃣
  - 6️⃣
  - 7️⃣
  - 8️⃣
  - 9️⃣
  - 🔟
  - 🔠
  - 🔡
  - 🔢
  - 🔣
  - 🔤
  - 🅰️
  - 🆎
  - 🅱️
  - 🆑
  - 🆒
  - 🆓
  - i️
  - 🆔
  - M️
  - 🆕
  - 🆖
  - 🅾️
  - 🆗
  - 🅿️
  - 🆘
  - 🆙
  - 🆚
  - ココ
  - サ️
  - 月️
  - 有
  - 指
  - 得
  - 割
  - 無
  - 禁
  - 可
  - 申
  - 合
  - 空
  - 祝️
  - 秘️
  - 営
  - 満
  - 🔴
  - 🟠
  - 🟡
  - 🟢
  - 🔵
  - 🟣
  - 🟤
  - ⚫
  - ⚪
  - 🟥
  - 🟧
  - 🟨
  - 🟩
  - 🟦
  - 🟪
  - 🟫
  - ⬛
  - ⬜
  - ◼️
  - ◻️
  - ◾
  - ◽
  - ▪️
  - ▫️
  - 🔶
  - 🔷
  - 🔸
  - 🔹
  - 🔺
  - 🔻
  - 💠
  - 🔘
  - 🔳
  - 🔲
  旗帜:
  - 🏁
  - 🚩
  - 🎌
  - 🏴
  - 🏳️
  - 🏳️‍🌈
  - 🏳️‍⚧️
  - 🏴‍☠️
  - 🇦🇨
  - 🇦🇩
  - 🇦🇪
  - 🇦🇫
  - 🇦🇬
  - 🇦🇮
  - 🇦🇱
  - 🇦🇲
  - 🇦🇴
  - 🇦🇶
  - 🇦🇷
  - 🇦🇸
  - 🇦🇹
  - 🇦🇺
  - 🇦🇼
  - 🇦🇽
  - 🇦🇿
  - 🇧🇦
  - 🇧🇧
  - 🇧🇩
  - 🇧🇪
  - 🇧🇫
  - 🇧🇬
  - 🇧🇭
  - 🇧🇮
  - 🇧🇯
  - 🇧🇱
  - 🇧🇲
  - 🇧🇳
  - 🇧🇴
  - 🇧🇶
  - 🇧🇷
  - 🇧🇸
  - 🇧🇹
  - 🇧🇻
  - 🇧🇼
  - 🇧🇾
  - 🇧🇿
  - 🇨🇦
  - 🇨🇨
  - 🇨🇩
  - 🇨🇫
  - 🇨🇬
  - 🇨🇭
  - 🇨🇮
  - 🇨🇰
  - 🇨🇱
  - 🇨🇲
  - 🇨🇳
  - 🇨🇴
  - 🇨🇵
  - 🇨🇷
  - 🇨🇺
  - 🇨🇻
  - 🇨🇼
  - 🇨🇽
  - 🇨🇾
  - 🇨🇿
  - 🇩🇪
  - 🇩🇬
  - 🇩🇯
  - 🇩🇰
  - 🇩🇲
  - 🇩🇴
  - 🇩🇿
  - 🇪🇦
  - 🇪🇨
  - 🇪🇪
  - 🇪🇬
  - 🇪🇭
  - 🇪🇷
  - 🇪🇸
  - 🇪🇹
  - 🇪🇺
  - 🇫🇮
  - 🇫🇯
  - 🇫🇰
  - 🇫🇲
  - 🇫🇴
  - 🇫🇷
  - 🇬🇦
  - 🇬🇧
  - 🇬🇩
  - 🇬🇪
  - 🇬🇫
  - 🇬🇬
  - 🇬🇭
  - 🇬🇮
  - 🇬🇱
  - 🇬🇲
  - 🇬🇳
  - 🇬🇵
  - 🇬🇶
  - 🇬🇷
  - 🇬🇸
  - 🇬🇹
  - 🇬🇺
  - 🇬🇼
  - 🇬🇾
  - 🇭🇰
  - 🇭🇲
  - 🇭🇳
  - 🇭🇷
  - 🇭🇹
  - 🇭🇺
  - 🇮🇨
  - 🇮🇩
  - 🇮🇪
  - 🇮🇱
  - 🇮🇲
  - 🇮🇳
  - 🇮🇴
  - 🇮🇶
  - 🇮🇷
  - 🇮🇸
  - 🇮🇹
  - 🇯🇪
  - 🇯🇲
  - 🇯🇴
  - 🇯🇵
  - 🇰🇪
  - 🇰🇬
  - 🇰🇭
  - 🇰🇮
  - 🇰🇲
  - 🇰🇳
  - 🇰🇵
  - 🇰🇷
  - 🇰🇼
  - 🇰🇾
  - 🇰🇿
  - 🇱🇦
  - 🇱🇧
  - 🇱🇨
  - 🇱🇮
  - 🇱🇰
  - 🇱🇷
  - 🇱🇸
  - 🇱🇹
  - 🇱🇺
  - 🇱🇻
  - 🇱🇾
  - 🇲🇦
  - 🇲🇨
  - 🇲🇩
  - 🇲🇪
  - 🇲🇫
  - 🇲🇬
  - 🇲🇭
  - 🇲🇰
  - 🇲🇱
  - 🇲🇲
  - 🇲🇳
  - 🇲🇴
  - 🇲🇵
  - 🇲🇶
  - 🇲🇷
  - 🇲🇸
  - 🇲🇹
  - 🇲🇺
  - 🇲🇻
  - 🇲🇼
  - 🇲🇽
  - 🇲🇾
  - 🇲🇿
  - 🇳🇦
  - 🇳🇨
  - 🇳🇪
  - 🇳🇫
  - 🇳🇬
  - 🇳🇮
  - 🇳🇱
  - 🇳🇴
  - 🇳🇵
  - 🇳🇷
  - 🇳🇺
  - 🇳🇿
  - 🇴🇲
  - 🇵🇦
  - 🇵🇪
  - 🇵🇫
  - 🇵🇬
  - 🇵🇭
  - 🇵🇰
  - 🇵🇱
  - 🇵🇲
  - 🇵🇳
  - 🇵🇷
  - 🇵🇸
  - 🇵🇹
  - 🇵🇼
  - 🇵🇾
  - 🇶🇦
  - 🇷🇪
  - 🇷🇴
  - 🇷🇸
  - 🇷🇺
  - 🇷🇼
  - 🇸🇦
  - 🇸🇧
  - 🇸🇨
  - 🇸🇩
  - 🇸🇪
  - 🇸🇬
  - 🇸🇭
  - 🇸🇮
  - 🇸🇯
  - 🇸🇰
  - 🇸🇱
  - 🇸🇲
  - 🇸🇳
  - 🇸🇴
  - 🇸🇷
  - 🇸🇸
  - 🇸🇹
  - 🇸🇻
  - 🇸🇽
  - 🇸🇾
  - 🇸🇿
  - 🇹🇦
  - 🇹🇨
  - 🇹🇩
  - 🇹🇫
  - 🇹🇬
  - 🇹🇭
  - 🇹🇯
  - 🇹🇰
  - 🇹🇱
  - 🇹🇲
  - 🇹🇳
  - 🇹🇴
  - 🇹🇷
  - 🇹🇹
  - 🇹🇻
  - 🇹🇼
  - 🇹🇿
  - 🇺🇦
  - 🇺🇬
  - 🇺🇲
  - 🇺🇳
  - 🇺🇸
  - 🇺🇾
  - 🇺🇿
  - 🇻🇦
  - 🇻🇨
  - 🇻🇪
  - 🇻🇬
  - 🇻🇮
  - 🇻🇳
  - 🇻🇺
  - 🇼🇫
  - 🇼🇸
  - 🇽🇰
  - 🇾🇪
  - 🇾🇹
  - 🇿🇦
  - 🇿🇲
  - 🇿🇼
  - 🏴󠁧󠁢󠁥󠁮󠁧󠁿
  - 🏴󠁧󠁢󠁳󠁣󠁴󠁿
  - 🏴󠁧󠁢󠁷󠁬󠁳󠁿
