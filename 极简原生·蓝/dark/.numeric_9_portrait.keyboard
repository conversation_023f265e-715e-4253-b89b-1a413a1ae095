"\u9884\u7F16\u8F91\u533A\u9AD8\u5EA6": 26
"\u5DE5\u5177\u680F\u9AD8\u5EA6": 40
"\u952E\u76D8\u533A\u9AD8\u5EA6": 226
"\u7F16\u7801\u5B57\u4F53\u5927\u5C0F": 0.88em
"\u6A2A\u6392\u5E8F\u53F7\u5B57\u4F53\u5927\u5C0F": 1.19em
"\u6A2A\u6392\u6587\u5B57\u5B57\u4F53\u5927\u5C0F": 1.19em
"\u6A2A\u6392\u6CE8\u91CA\u5B57\u4F53\u5927\u5C0F": 0.81em
"\u5C55\u5F00\u5E8F\u53F7\u5B57\u4F53\u5927\u5C0F": 1.125em
"\u5C55\u5F00\u6587\u5B57\u5B57\u4F53\u5927\u5C0F": 1.125em
"\u5C55\u5F00\u6CE8\u91CA\u5B57\u4F53\u5927\u5C0F": 0.75em
"\u7F16\u7801\u8272": FDFDFD
"\u9996\u9009\u8272": 4c8dff
"\u6B21\u9009\u8272": FDFDFD
"\u952E\u76D8\u5E95\u8272": 2D2D2D
"\u6309\u952E\u5B57\u7B26\u6B63\u5E38\u8272": FFFFFF
"\u6309\u952E\u5B57\u7B26\u9AD8\u4EAE\u8272": FFFFFF
"\u56DE\u8F66\u952E\u5B57\u7B26\u8272": FFFFFF
"\u72B6\u6001\u680F\u56FE\u6807\u7F29\u653E": 0.55
"\u6536\u8D77\u56FE\u6807":
  fontSize: 1em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u5C55\u5F00\u5019\u9009\u5217\u8868\u80CC\u666F\u8272": 66696e
"\u5C55\u5F00\u5019\u9009\u529F\u80FD\u952E\u5B57\u7B26\u6837\u5F0F":
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u5217\u8868\u6587\u5B57\u6837\u5F0F":
  normalColor: FFFFFF
  fontSize: 0.875em
"\u5217\u8868\u680F\u9AD8\u4EAE\u80CC\u666F":
  type: original
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  normalColor: 66696e
  cornerRadius: 6
"\u6570\u5B57\u952E\u80CC\u666F":
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
"\u529F\u80FD\u952E\u80CC\u666F":
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
"\u56DE\u8F66\u952E\u80CC\u666F":
  type: original
  normalColor: 3379f5
  highlightColor: 296bdf
  cornerRadius: 9
  normalLowerEdgeColor: 222222
  highlightLowerEdgeColor: 222222
"\u6570\u5B57\u952E\u524D\u666F":
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"sym\u524D\u666F":
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"return\u524D\u666F":
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"space\u524D\u666F":
  center:
    x: 0.5
    y: 0.5
  fontSize: 1.25em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"Backspace\u524D\u666F":
  center:
    x: 0.5
    y: 0.5
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"enter\u524D\u666F":
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"period\u952E\u524D\u666F":
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.125em
  normalColor: FFFFFF
  highlightColor: FFFFFF
"equal\u524D\u666F":
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
preeditBackgroundStyle:
  type: original
  normalColor: 2D2D2D
toolbarBackgroundStyle:
  type: original
  normalColor: 2D2D2D
verticalCandidateBackgroundStyle:
  type: original
  normalColor: 2D2D2D
keyboardBackgroundStyle:
  type: original
  normalColor: 2D2D2D
animation:
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 1
  toScale: 0.8
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 0.8
  toScale: 1
preeditHeight: 26
toolbarHeight: 40
keyboardHeight: 226
preedit:
  insets:
    left: 10
    top: 2
  backgroundStyle: preeditBackgroundStyle
  foregroundStyle: preeditForegroundStyle
preeditForegroundStyle:
  textColor: FDFDFD
  fontSize: 0.88em
toolbar:
  backgroundStyle: toolbarBackgroundStyle
  primaryButtonStyle: primaryButtonStyle
  secondaryButtonStyle:
  - toolbarButtonHideStyle
  - toolbarButton1Style
  - toolbarButton2Style
  - toolbarButton3Style
  - toolbarButton4Style
  - toolbarButton5Style
  - toolbarButton6Style
  horizontalCandidateStyle: horizontalCandidateStyle
  verticalCandidateStyle: verticalCandidateStyle
primaryButtonStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - primaryButtonForegroundStyle
  action:
    floatKeyboardType: floatconfig
primaryButtonForegroundStyle:
  normalImage:
    file: "\u8BBE\u7F6E"
    image: IMG1
  highlightImage:
    file: "\u8BBE\u7F6E"
    image: IMG1
  targetScale: 0.55
toolbarButtonHideStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButtonHideForegroundStyle
  action: dismissKeyboard
toolbarButtonHideForegroundStyle:
  normalImage:
    file: "\u6536\u8D77"
    image: IMG1
  highlightImage:
    file: "\u6536\u8D77"
    image: IMG1
  targetScale: 0.55
toolbarButton1Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton1ForegroundStyle
  action:
    shortcutCommand: '#showPasteboardView'
toolbarButton1ForegroundStyle:
  normalImage:
    file: "\u526A\u8D34"
    image: IMG1
  highlightImage:
    file: "\u526A\u8D34"
    image: IMG1
  targetScale: 0.55
toolbarButton2Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton2ForegroundStyle
  action:
    shortcutCommand: '#showPhraseView'
toolbarButton2ForegroundStyle:
  normalImage:
    file: "\u77ED\u8BED"
    image: IMG1
  highlightImage:
    file: "\u77ED\u8BED"
    image: IMG1
  targetScale: 0.55
toolbarButton3Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton3ForegroundStyle
  action:
    shortcutCommand: '#RimeSwitcher'
toolbarButton3ForegroundStyle:
  normalImage:
    file: "\u5F00\u5173"
    image: IMG1
  highlightImage:
    file: "\u5F00\u5173"
    image: IMG1
  targetScale: 0.55
toolbarButton4Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton4ForegroundStyle
  action:
    floatKeyboardType: floatsearch
toolbarButton4ForegroundStyle:
  normalImage:
    file: "\u641C\u7D22"
    image: IMG1
  highlightImage:
    file: "\u641C\u7D22"
    image: IMG1
  targetScale: 0.55
toolbarButton5Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton5ForegroundStyle
  action:
    runTranslateScript: "\u667A\u8C31\u7FFB\u8BD1"
toolbarButton5ForegroundStyle:
  normalImage:
    file: "\u7FFB\u8BD1"
    image: IMG1
  highlightImage:
    file: "\u7FFB\u8BD1"
    image: IMG1
  targetScale: 0.55
toolbarButton6Style:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle:
  - toolbarButton6ForegroundStyle
  action:
    openURL: hamster://dev.fuxiao.app.hamster/keyboardSkins
toolbarButton6ForegroundStyle:
  normalImage:
    file: "\u76AE\u80A4"
    image: IMG1
  highlightImage:
    file: "\u76AE\u80A4"
    image: IMG1
  targetScale: 0.55
horizontalCandidateStyle:
  insets:
    left: 5
    top: 5
    bottom: 5
  candidateStateButtonStyle: candidateStateButtonStyle
  highlightBackgroundColor: 00000000
  preferredBackgroundColor: 00000000
  preferredIndexColor: 4c8dff
  preferredTextColor: 4c8dff
  preferredCommentColor: 4c8dff
  indexColor: FDFDFD
  textColor: FDFDFD
  commentColor: FDFDFD
  indexFontSize: 1.19em
  textFontSize: 1.19em
  commentFontSize: 0.81em
  itemSpacing: 5
candidateStateButtonStyle:
  backgroundStyle: toolbarButtonBackgroundStyle
  foregroundStyle: candidateStateButtonForegroundStyle
candidateStateButtonForegroundStyle:
  systemImageName: chevron.down
  fontSize: 1em
  normalColor: FFFFFF
  highlightColor: FFFFFF
verticalCandidateStyle:
  insets:
    top: 3
    bottom: 3
    left: 4
    right: 4
  bottomRowHeight: 43
  backgroundStyle: verticalCandidateBackgroundStyle
  candidateStyle: verticalCandidateOfCandidateStyle
  pageUpButtonStyle: verticalCandidatePageUpButtonStyle
  pageDownButtonStyle: verticalCandidatePageDownButtonStyle
  returnButtonStyle: verticalCandidateReturnButtonStyle
  backspaceButtonStyle: verticalCandidateBackspaceButtonStyle
verticalCandidateOfCandidateStyle:
  insets:
    top: 3
    bottom: 6
    left: 8
    right: 8
  cornerRadius: 9
  backgroundColor: 66696e
  separatorColor: 00000000
  highlightBackgroundColor: 00000000
  preferredBackgroundColor: 00000000
  preferredIndexColor: FDFDFD
  preferredTextColor: FDFDFD
  preferredCommentColor: FDFDFD
  indexColor: FDFDFD
  textColor: FDFDFD
  commentColor: FDFDFD
  indexFontSize: 1.125em
  textFontSize: 1.125em
  commentFontSize: 0.75em
verticalCandidatePageUpButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidatePageUpButtonForegroundStyle
verticalCandidateButtonBackgroundStyle:
  insets:
    top: 3
    left: 8
    bottom: 3
    right: 8
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
verticalCandidatePageUpButtonForegroundStyle:
  text: "\uE991"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: FFFFFF
  highlightColor: FFFFFF
verticalCandidatePageDownButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidatePageDownButtonForegroundStyle
verticalCandidatePageDownButtonForegroundStyle:
  text: "\uE992"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: FFFFFF
  highlightColor: FFFFFF
verticalCandidateReturnButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle: verticalCandidateReturnButtonForegroundStyle
verticalCandidateReturnButtonForegroundStyle:
  text: "\uE9B8"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: FFFFFF
  highlightColor: FFFFFF
verticalCandidateBackspaceButtonStyle:
  backgroundStyle: verticalCandidateButtonBackgroundStyle
  foregroundStyle:
  - verticalCandidateBackspaceButtonForegroundStyle
verticalCandidateBackspaceButtonForegroundStyle:
  text: "\uE9AA"
  center:
    y: 0.8
  fontSize: 0.88em
  normalColor: FFFFFF
  highlightColor: FFFFFF
keyboard:
  style: keyboardStyle
  subviews:
  - VStack:
      style: VStackStyle
      subviews:
      - Cell: collection
      - Cell: symbolButton
  - VStack:
      subviews:
      - Cell: 1Button
      - Cell: 4Button
      - Cell: 7Button
      - Cell: returnButton
  - VStack:
      subviews:
      - Cell: 2Button
      - Cell: 5Button
      - Cell: 8Button
      - Cell: 0Button
  - VStack:
      subviews:
      - Cell: 3Button
      - Cell: 6Button
      - Cell: 9Button
      - Cell: spaceButton
  - VStack:
      style: VStackStyle
      subviews:
      - Cell: backspaceButton
      - Cell: periodButton
      - Cell: equalButton
      - Cell: enterButton
keyboardStyle:
  insets:
    top: 1
  backgroundStyle: keyboardBackgroundStyle
VStackStyle:
  size:
    width: 1.6/10
collection:
  backgroundStyle: collectionBackgroundStyle
  type: symbols
  dataSource: symbols
  cellStyle: collectionCellStyle
collectionBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
collectionCellStyle:
  backgroundStyle: collectionCellBackgroundStyle
  foregroundStyle: collectionCellForegroundStyle
collectionCellBackgroundStyle:
  type: original
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  normalColor: 66696e
  cornerRadius: 6
collectionCellForegroundStyle:
  normalColor: FFFFFF
  fontSize: 0.875em
symbolButton:
  size:
    height: 53/226
  backgroundStyle: symbolButtonBackgroundStyle
  foregroundStyle:
  - symbolButtonForegroundStyle
  action:
    keyboardType: symbolic
  swipeUpAction:
    keyboardType: emoji
symbolButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  animation: animation
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
symbolButtonForegroundStyle:
  animation: animation
  text: "\u7B26"
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
1Button:
  backgroundStyle: 1ButtonBackgroundStyle
  foregroundStyle:
  - 1ButtonForegroundStyle
  action:
    character: 1
1ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
1ButtonForegroundStyle:
  animation: animation
  text: '1'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
4Button:
  backgroundStyle: 4ButtonBackgroundStyle
  foregroundStyle:
  - 4ButtonForegroundStyle
  action:
    character: 4
4ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
4ButtonForegroundStyle:
  animation: animation
  text: '4'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
7Button:
  backgroundStyle: 7ButtonBackgroundStyle
  foregroundStyle:
  - 7ButtonForegroundStyle
  action:
    character: 7
7ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
7ButtonForegroundStyle:
  animation: animation
  text: '7'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
returnButton:
  size:
    height: 53/226
  backgroundStyle: returnButtonBackgroundStyle
  foregroundStyle:
  - returnButtonForegroundStyle
  action: returnPrimaryKeyboard
returnButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  animation: animation
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
returnButtonForegroundStyle:
  animation: animation
  text: "\u8FD4\u56DE"
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
2Button:
  backgroundStyle: 2ButtonBackgroundStyle
  foregroundStyle:
  - 2ButtonForegroundStyle
  action:
    character: 2
2ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
2ButtonForegroundStyle:
  animation: animation
  text: '2'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
5Button:
  backgroundStyle: 5ButtonBackgroundStyle
  foregroundStyle:
  - 5ButtonForegroundStyle
  action:
    character: 5
5ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
5ButtonForegroundStyle:
  animation: animation
  text: '5'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
8Button:
  backgroundStyle: 8ButtonBackgroundStyle
  foregroundStyle:
  - 8ButtonForegroundStyle
  action:
    character: 8
8ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
8ButtonForegroundStyle:
  animation: animation
  text: '8'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
0Button:
  size:
    height: 53/226
  backgroundStyle: 0ButtonBackgroundStyle
  foregroundStyle:
  - 0ButtonForegroundStyle
  action:
    character: 0
0ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
0ButtonForegroundStyle:
  animation: animation
  text: '0'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
3Button:
  backgroundStyle: 3ButtonBackgroundStyle
  foregroundStyle:
  - 3ButtonForegroundStyle
  action:
    character: 3
3ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
3ButtonForegroundStyle:
  animation: animation
  text: '3'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
6Button:
  backgroundStyle: 6ButtonBackgroundStyle
  foregroundStyle:
  - 6ButtonForegroundStyle
  action:
    character: 6
6ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
6ButtonForegroundStyle:
  animation: animation
  text: '6'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
9Button:
  backgroundStyle: 9ButtonBackgroundStyle
  foregroundStyle:
  - 9ButtonForegroundStyle
  action:
    character: 9
9ButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
9ButtonForegroundStyle:
  animation: animation
  text: '9'
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
spaceButton:
  size:
    height: 53/226
  backgroundStyle: spaceButtonBackgroundStyle
  foregroundStyle: spaceButtonForegroundStyle
  action: space
spaceButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  animation: animation
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
spaceButtonForegroundStyle:
  animation: animation
  systemImageName: space
  center:
    x: 0.5
    y: 0.5
  fontSize: 1.25em
  normalColor: FFFFFF
  highlightColor: FFFFFF
backspaceButton:
  backgroundStyle: backspaceButtonBackgroundStyle
  foregroundStyle: backspaceButtonForegroundStyle
  action: backspace
  repeatAction: backspace
backspaceButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
backspaceButtonForegroundStyle:
  animation: animation
  systemImageName: delete.left
  center:
    x: 0.5
    y: 0.5
  fontSize: 1.06em
  normalColor: FFFFFF
  highlightColor: FFFFFF
periodButton:
  backgroundStyle: periodButtonBackgroundStyle
  foregroundStyle: periodButtonForegroundStyle
  action:
    character: .
periodButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
periodButtonForegroundStyle:
  animation: animation
  text: "\xB7"
  center:
    x: 0.5
    y: 0.8
  fontSize: 1.125em
  normalColor: FFFFFF
  highlightColor: FFFFFF
equalButton:
  backgroundStyle: equalButtonBackgroundStyle
  foregroundStyle: equalButtonForegroundStyle
  action:
    character: '='
equalButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 5
    right: 3
  animation: animation
  type: original
  normalColor: 4a4d50
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
equalButtonForegroundStyle:
  animation: animation
  text: '='
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
enterButton:
  size:
    height: 53/226
  backgroundStyle: enterButtonBackgroundStyle
  foregroundStyle:
  - enterButtonForegroundStyle
  action: enter
enterButtonBackgroundStyle:
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  animation: animation
  type: original
  normalColor: 3379f5
  highlightColor: 296bdf
  cornerRadius: 9
  normalLowerEdgeColor: 222222
  highlightLowerEdgeColor: 222222
enterButtonForegroundStyle:
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  const type = $getReturnKeyType();\n
    \ switch (type) {\n    case 1:\n      return \"\u524D\u5F80\";\n    case 3:\n
    \     return \"\u52A0\u5165\";\n    case 4:\n      return \"\u524D\u5F80\";\n
    \   case 6:\n      return \"\u641C\u7D22\"\n    case 7:\n      return \"\u53D1\u9001\"\n
    \   case 9:\n      return \"\u5B8C\u6210\";\n    default:\n      return \"\u6362\u884C\";\n
    \ }\n}"
  center:
    x: 0.5
    y: 0.8
  fontSize: 0.94em
  normalColor: FFFFFF
  highlightColor: FFFFFF
dataSource:
  symbols:
  - +
  - '-'
  - '*'
  - /
  - ()
  - ','
  - '#'
  - '%'
  - ':'
  - _
  - '?'
  - "\uFFE5"
