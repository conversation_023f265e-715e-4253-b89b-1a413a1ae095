"\u6309\u952E\u80CC\u666F":
  type: original
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
"\u6309\u952E\u6587\u5B57\u524D\u666F\u6837\u5F0F":
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
keyboard:
  style: keyboardStyle
  subviews:
  - HStack:
      subviews:
      - Cell: "\u7B80\u7E41"
      - Cell: "\u5168\u534A"
      - Cell: "\u4E2D\u82F1\u6807\u70B9"
      - Cell: "\u62FC\u97F3"
  - HStack:
      subviews:
      - Cell: "\u5B57\u96C6"
      - Cell: "\u8BCD\u7EC4"
      - Cell: "\u8868\u60C5"
      - Cell: "\u62C6\u5206"
floatTargetScale:
  x: 0.6
  y: 0.4
keyboardStyle:
  insets:
    top: 8
    left: 8
    bottom: 8
    right: 8
  backgroundStyle: keyboardBackgroundStyle
keyboardBackgroundStyle:
  type: original
  normalColor: 2D2D2DDD
  cornerRadius: 9
  borderSize: 0.5
  normalBorderColor: 22222255
  normalLowerEdgeColor: 55555555
"\u7B80\u7E41":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u7B80\u7E41ForegroundStyle"
  action:
    sendKeys: Control+o
ButtonBackgroundStyle:
  animation: animation
  type: original
  insets:
    top: 5
    left: 3
    bottom: 6
    right: 3
  normalColor: 66696e
  highlightColor: 4a4d50
  cornerRadius: 9
  normalLowerEdgeColor: 181818
  highlightLowerEdgeColor: 181818
"\u7B80\u7E41ForegroundStyle":
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  return $getRimeOptionState(\"simplification\")
    ? \"\u7E41\u4F53\" : \"\u7B80\u4F53\";\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u5168\u534A":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u5168\u534AForegroundStyle"
  action:
    sendKeys: Shift+space
"\u5168\u534AForegroundStyle":
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  return $getRimeOptionState(\"full_shape\")
    ? \"\u5168\u89D2\" : \"\u534A\u89D2\";\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u4E2D\u82F1\u6807\u70B9":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u4E2D\u82F1\u6807\u70B9ForegroundStyle"
  action:
    sendKeys: Control+period
"\u4E2D\u82F1\u6807\u70B9ForegroundStyle":
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  return $getRimeOptionState(\"ascii_punct\")
    ? \"\u82F1\u6807\" : \"\u4E2D\u6807\";\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u62FC\u97F3":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u62FC\u97F3ForegroundStyle"
  action:
    sendKeys: Control+p
"\u62FC\u97F3ForegroundStyle":
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  return $getRimeOptionState(\"pinyin\")
    ? \"P\u012Bn\" : \"\u62FC\u97F3\";\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u5B57\u96C6":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u5B57\u96C6ForegroundStyle"
  action:
    sendKeys: Control+h
"\u5B57\u96C6ForegroundStyle":
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  return $getRimeOptionState(\"charset_filter\")
    ? \"\u5E38\u7528\" : \"\u6269\u5C55\";\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u8BCD\u7EC4":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u8BCD\u7EC4ForegroundStyle"
  action:
    sendKeys: Control+k
"\u8BCD\u7EC4ForegroundStyle":
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  return $getRimeOptionState(\"single_char_only\")
    ? \"\u5355\u5B57\" : \"\u8BCD\u7EC4\";\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u8868\u60C5":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u8868\u60C5ForegroundStyle"
  action:
    sendKeys: Control+i
"\u8868\u60C5ForegroundStyle":
  animation: animation
  text: "// JavaScript\nfunction getText() {\n  return $getRimeOptionState(\"emoji_cn\")
    ? \"\u8868\u60C5\" : \"\u95ED\u5634\";\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
"\u62C6\u5206":
  size:
    height: 1/4
  backgroundStyle: ButtonBackgroundStyle
  foregroundStyle:
  - "\u62C6\u5206ForegroundStyle"
  action:
    sendKeys: Shift+Control+j
"\u62C6\u5206ForegroundStyle":
  animation: animation
  text: "// JavaScript: \u6839\u636E\u5F53\u524D\u9009\u9879\u663E\u793A\u62FC\u97F3\u6CE8\u89E3\u7B49\u7EA7\nfunction
    getText() {\n  let level = $getRimeOptionState(\"spelling.lv1\") ? \"\u4E00\u91CD\"
    :\n             $getRimeOptionState(\"spelling.lv2\") ? \"\u4E8C\u91CD\" :\n             $getRimeOptionState(\"spelling.lv3\")
    ? \"\u4E09\u91CD\" : \"\u3007\u91CD\";\n  return level;\n}"
  fontSize: 0.88em
  center:
    y: 0.8
  normalColor: FFFFFF
  highlightColor: FFFFFF
animation:
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 1
  toScale: 0.8
- type: bounds
  duration: 80
  repeatCount: 1
  fromScale: 0.8
  toScale: 1
